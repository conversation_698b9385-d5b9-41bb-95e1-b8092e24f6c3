QT       += core gui sql widgets serialport network axcontainer

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    BatchCalibration.cpp \
    BatchVerification.cpp \
    CalibrationDialog.cpp \
    CalibrationWorker.cpp \
    DbHandler.cpp \
    ExportTask.cpp \
    NetworkHandler.cpp \
    NetworkWorker.cpp \
    ProjectDetailsDialog.cpp \
    SerialHandler.cpp \
    SerialWorker.cpp \
    TC1618AVoltageVerificationWorker.cpp \
    VerificationDialog.cpp \
    VerificationWorker.cpp \
    VoltageCalibrationWorker.cpp \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    BatchCalibration.h \
    BatchVerification.h \
    CalibrationDialog.h \
    CalibrationWorker.h \
    DataProcessor.h \
    DbHandler.h \
    ExportTask.h \
    NetworkHandler.h \
    NetworkWorker.h \
    ProjectDataTypes.h \
    ProjectDetailsDialog.h \
    SerialHandler.h \
    SerialWorker.h \
    TC1618AVoltageVerificationWorker.h \
    VerificationDialog.h \
    VerificationWorker.h \
    VoltageCalibrationWorker.h \
    WorkerConfig.h \
    mainwindow.h

FORMS += \
    CalibrationDialog.ui \
    VerificationDialog.ui \
    mainwindow.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

# 设置编码格式为UTF-8
QMAKE_CXXFLAGS += -utf-8

RC_FILE = exe_ico.rc

RESOURCES += \
    resources.qrc
