#include "TC1618AVoltageVerificationWorker.h"
#include <QApplication>
#include <QThread>
#include <cmath>

// 静态常量定义 - 五个电压档位
const QVector<TC1618AVoltageVerificationWorker::VoltageLevel> TC1618AVoltageVerificationWorker::VOLTAGE_LEVELS = {
    {-10.0, "-10mV"},
    {10.0, "10mV"},
    {30.0, "30mV"},
    {50.0, "50mV"},
    {75.0, "75mV"}};

TC1618AVoltageVerificationWorker::TC1618AVoltageVerificationWorker(QObject *parent)
    : QObject(parent),
      m_abortRequested(false),
      m_dataCollectionTimer(new QTimer(this)),
      m_currentChannel(0),
      m_currentLevel(0),
      m_currentRound(0),
      m_isRecalibrationMode(false),
      m_recalibrationChannel(-1),
      m_recalibrationLevel(-1),
      m_calC<PERSON><PERSON><PERSON>andler(nullptr)
{
    m_dataCollectionTimer->setSingleShot(true);
    connect(m_dataCollectionTimer, &QTimer::timeout, this, &TC1618AVoltageVerificationWorker::collectVoltageData);
}

TC1618AVoltageVerificationWorker::~TC1618AVoltageVerificationWorker()
{
}

void TC1618AVoltageVerificationWorker::setDeviceConfig(const CalDeviceConfig &config)
{
    m_deviceConfig = config;
}

void TC1618AVoltageVerificationWorker::setCommandHandlers(CommandHandlerFunc calHandler)
{
    m_calCommandHandler = calHandler;
}

void TC1618AVoltageVerificationWorker::requestAbort()
{
    m_abortRequested = true;
    if (m_dataCollectionTimer->isActive())
    {
        m_dataCollectionTimer->stop();
    }

    emit logMessage("1618A TC电压校准被用户中止");
    emit voltageCalibrationFinished(false);
}

void TC1618AVoltageVerificationWorker::startVoltageCalibration()
{
    emit logMessage("开始1618A TC手动电压校准...");
    emit logMessage(QString("设备型号: %1").arg(m_deviceConfig.name));

    // 清空表格数据
    emit clearVoltageTable();

    m_abortRequested = false;
    m_isRecalibrationMode = false;
    m_currentChannel = 0; // 从CH1开始
    m_currentLevel = 0;   // 从-10mV开始

    // 初始进度条：0%开始
    emit voltageCalibrationProgress(0, 100);

    // 开始第一个通道第一个电压档位的校准
    emit logMessage(QString("开始校准CH%1通道的%2档位...").arg(m_currentChannel + 1).arg(VOLTAGE_LEVELS[m_currentLevel].description));
    showUserPrompt();
}

void TC1618AVoltageVerificationWorker::startSingleItemRecalibration(int channel, int level)
{
    if (channel < 0 || channel >= 8 || level < 0 || level >= VOLTAGE_LEVELS.size())
    {
        emit logMessage("复校参数无效");
        return;
    }

    emit logMessage(QString("开始复校CH%1通道的%2档位...").arg(channel + 1).arg(VOLTAGE_LEVELS[level].description));

    m_abortRequested = false;
    m_isRecalibrationMode = true;
    m_recalibrationChannel = channel;
    m_recalibrationLevel = level;
    m_currentChannel = channel;
    m_currentLevel = level;

    showUserPrompt();
}

void TC1618AVoltageVerificationWorker::showUserPrompt()
{
    if (m_abortRequested)
        return;

    double targetVoltage = VOLTAGE_LEVELS[m_currentLevel].targetVoltage;
    QString message = QString("请将754校准器连接至CH%1通道并设置输出%2mV，设置完成后点击"确定
                              "继续，点击"取消
                              "退出校准流程")
                          .arg(m_currentChannel + 1)
                          .arg(targetVoltage);

    emit logMessage(QString("等待用户设置754校准器连接至CH%1通道并输出%2mV...").arg(m_currentChannel + 1).arg(targetVoltage));

    // 发送信号到主线程显示对话框
    emit requestUserPrompt(message);
}

void TC1618AVoltageVerificationWorker::onUserPromptResult(bool confirmed)
{
    if (m_abortRequested)
        return;

    if (confirmed)
    {
        double targetVoltage = VOLTAGE_LEVELS[m_currentLevel].targetVoltage;
        emit logMessage(QString("用户确认设置完成，等待2秒后开始读取CH%1通道的%2档位数据...").arg(m_currentChannel + 1).arg(VOLTAGE_LEVELS[m_currentLevel].description));

        // 等待2秒后开始数据采集
        QTimer::singleShot(2000, this, &TC1618AVoltageVerificationWorker::startDataCollection);
    }
    else
    {
        emit logMessage("用户取消1618A TC电压校准");
        finishCalibration(false);
    }
}

void TC1618AVoltageVerificationWorker::startDataCollection()
{
    if (m_abortRequested)
        return;

    // 重置数据采集状态
    m_currentRound = 0;
    m_voltageReadings.clear();

    emit logMessage(QString("开始采集CH%1通道%2档位数据，将以1秒间隔读取4次...").arg(m_currentChannel + 1).arg(VOLTAGE_LEVELS[m_currentLevel].description));

    // 立即开始第一次数据采集
    collectVoltageData();
}

void TC1618AVoltageVerificationWorker::collectVoltageData()
{
    if (m_abortRequested)
        return;

    if (m_currentRound >= MAX_ROUNDS)
    {
        // 4次数据采集完成，处理当前档位数据
        processCurrentLevelData();
        return;
    }

    // 读取电压值
    double voltage = readVoltageValue(m_currentChannel);
    if (voltage == 0.0 && m_currentRound > 0)
    {
        // 第一次读取可能为0，后续不应该为0
        emit logMessage(QString("第%1轮读取CH%2通道电压值失败").arg(m_currentRound + 1).arg(m_currentChannel + 1));
        finishCalibration(false);
        return;
    }

    m_voltageReadings.append(voltage);

    emit logMessage(QString("第%1轮读取: CH%2通道电压=%3mV")
                        .arg(m_currentRound + 1)
                        .arg(m_currentChannel + 1)
                        .arg(voltage, 0, 'f', 6));

    m_currentRound++;

    // 1秒后进行下一轮采集
    m_dataCollectionTimer->start(1000);
}

void TC1618AVoltageVerificationWorker::processCurrentLevelData()
{
    if (m_voltageReadings.size() != MAX_ROUNDS)
    {
        emit logMessage("数据采集不完整，校准失败");
        finishCalibration(false);
        return;
    }

    // 计算平均值
    double sum = 0.0;
    for (double voltage : m_voltageReadings)
    {
        sum += voltage;
    }
    double avgVoltage = sum / MAX_ROUNDS;

    // 计算偏差
    double targetVoltage = VOLTAGE_LEVELS[m_currentLevel].targetVoltage;
    double deviation = std::abs(avgVoltage - targetVoltage);

    // 计算允差
    double tolerance = calculateTolerance(avgVoltage);

    // 判断是否合格
    bool passed = (deviation <= tolerance);

    emit logMessage(QString("CH%1通道%2档位校准完成: 平均电压=%3mV, 偏差=%4mV, 允差=%5mV, %6")
                        .arg(m_currentChannel + 1)
                        .arg(VOLTAGE_LEVELS[m_currentLevel].description)
                        .arg(avgVoltage, 0, 'f', 6)
                        .arg(deviation, 0, 'f', 6)
                        .arg(tolerance, 0, 'f', 6)
                        .arg(passed ? "合格" : "不合格"));

    // 更新表格数据
    emit updateVoltageTableData(m_currentChannel, m_currentLevel, m_voltageReadings, avgVoltage, deviation, tolerance, passed);

    // 如果是复校模式，直接完成
    if (m_isRecalibrationMode)
    {
        emit logMessage("复校完成");
        finishCalibration(true);
        return;
    }

    // 移动到下一个档位或通道
    moveToNextLevel();
}

void TC1618AVoltageVerificationWorker::moveToNextLevel()
{
    m_currentLevel++;

    if (m_currentLevel >= VOLTAGE_LEVELS.size())
    {
        // 当前通道所有档位完成，移动到下一个通道
        moveToNextChannel();
    }
    else
    {
        // 开始当前通道的下一个档位
        emit logMessage(QString("开始校准CH%1通道的%2档位...").arg(m_currentChannel + 1).arg(VOLTAGE_LEVELS[m_currentLevel].description));
        showUserPrompt();
    }
}

void TC1618AVoltageVerificationWorker::moveToNextChannel()
{
    m_currentChannel++;
    m_currentLevel = 0; // 重置档位

    if (m_currentChannel >= 8) // CH1-CH8
    {
        // 所有通道校准完成
        emit logMessage("所有通道电压校准完成！");
        finishCalibration(true);
    }
    else
    {
        // 开始下一个通道的校准
        emit logMessage(QString("开始校准CH%1通道的%2档位...").arg(m_currentChannel + 1).arg(VOLTAGE_LEVELS[m_currentLevel].description));

        // 更新进度条
        int progress = (m_currentChannel * 100) / 8;
        emit voltageCalibrationProgress(progress, 100);

        showUserPrompt();
    }
}

void TC1618AVoltageVerificationWorker::finishCalibration(bool success)
{
    if (m_dataCollectionTimer->isActive())
    {
        m_dataCollectionTimer->stop();
    }

    QString result = success ? "1618A TC电压校准成功完成" : "1618A TC电压校准失败";
    emit logMessage(result);

    if (success)
    {
        emit voltageCalibrationProgress(100, 100);
    }

    emit voltageCalibrationFinished(success);
}

double TC1618AVoltageVerificationWorker::calculateTolerance(double measuredVoltage)
{
    // 允差计算公式：0.0001*测量电压值+0.01mV
    return 0.0001 * std::abs(measuredVoltage) + 0.01;
}

// 通信相关方法实现
QPair<bool, QString> TC1618AVoltageVerificationWorker::sendCommand(const QByteArray &command, const QString &commandId)
{
    // 调用MainWindow的命令处理函数
    if (m_calCommandHandler)
    {
        return m_calCommandHandler(command, commandId);
    }
    return {false, "命令处理函数未设置"};
}

QByteArray TC1618AVoltageVerificationWorker::createModbusCommand(const QString &hexString)
{
    // 移除所有空格
    QString cleanCommand = hexString.simplified().remove(' ');

    // 将十六进制字符串转换为 QByteArray
    QByteArray cmd = QByteArray::fromHex(cleanCommand.toLatin1());

    // 计算并添加 CRC
    uint16_t crc = calculateCRC16(cmd);
    cmd.append(static_cast<char>(crc & 0xFF));
    cmd.append(static_cast<char>((crc >> 8) & 0xFF));

    return cmd;
}

QByteArray TC1618AVoltageVerificationWorker::createModbusReadFrame(quint8 deviceAddr, quint16 startAddr, int channel, int count)
{
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr));
    frame.append(static_cast<char>(0x03)); // 读取功能码

    // 计算实际读取地址：起始地址 + 通道偏移
    quint16 actualAddr = startAddr + channel;
    frame.append(static_cast<char>((actualAddr >> 8) & 0xFF)); // 高字节
    frame.append(static_cast<char>(actualAddr & 0xFF));        // 低字节

    // 读取寄存器数量
    frame.append(static_cast<char>((count >> 8) & 0xFF)); // 高字节
    frame.append(static_cast<char>(count & 0xFF));        // 低字节

    return frame;
}

double TC1618AVoltageVerificationWorker::readVoltageValue(int channel)
{
    if (m_abortRequested)
        return 0.0;

    // 获取读取地址
    quint16 readAddr = getReadAddress();

    // 创建读取命令
    QByteArray frame = createModbusReadFrame(0x01, readAddr, channel, 1);
    auto result = sendCommand(createModbusCommand(QString::fromLatin1(frame.toHex())), "Cal_ReadVoltageValue_1618A_TC");

    if (result.first)
    {
        // 解析返回的电压值
        double voltage = result.second.toDouble();
        return voltage;
    }
    else
    {
        emit logMessage(QString("读取CH%1通道电压值失败: %2").arg(channel + 1).arg(result.second));
        return 0.0;
    }
}

uint16_t TC1618AVoltageVerificationWorker::calculateCRC16(const QByteArray &data)
{
    uint16_t crc = 0xFFFF;
    for (int i = 0; i < data.size(); ++i)
    {
        crc ^= static_cast<uint8_t>(data[i]);
        for (int j = 0; j < 8; ++j)
        {
            if (crc & 0x0001)
            {
                crc >>= 1;
                crc ^= 0xA001;
            }
            else
            {
                crc >>= 1;
            }
        }
    }
    return crc;
}

quint16 TC1618AVoltageVerificationWorker::getReadAddress() const
{
    // 根据设备型号返回读取首地址
    if (m_deviceConfig.name.contains("1618A-N"))
    {
        return 0x033D; // 1618A-N的TC读取首地址：03 3D
    }
    else if (m_deviceConfig.name.contains("1618A-L"))
    {
        return 0x015D; // 1618A-L的TC读取首地址：01 5D
    }
    else
    {
        emit logMessage("警告：未知的设备型号，使用默认地址");
        return 0x033D; // 默认使用1618A-N的地址
    }
}
