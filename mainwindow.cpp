#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QHostAddress>
#include <QJsonArray>
#include <QJsonDocument>
#include <QSqlQuery>
#include <QSqlError>
#include <QPropertyAnimation>
#include <QThreadPool>
#include <QCloseEvent>

// 初始化静态成员
MainWindow *MainWindow::s_instance = nullptr;

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::MainWindow), m_calDeviceProcessor_Serial(new calDeviceProcessor(this)), m_calDeviceProcessor_Network(new calDeviceProcessor(this)), m_calDeviceSerialHandler(new SerialHandlerPro(this)), m_calDeviceNetworkHandler(new NetworkHandler(this)), m_1220DeviceProcessor_Serial(new _1220DeviceProcessor(this)), m_1220DeviceProcessor_Network(new _1220DeviceProcessor(this)), m_1220DeviceSerialHandler(new SerialHandlerPro(this)), m_1220DeviceNetworkHandler(new NetworkHandler(this)), dbHandler(new DbHandler(this)), calibrationThread(nullptr), calibrationWorker(nullptr), verificationThread(nullptr), verificationWorker(nullptr), projectDetailsDialog(nullptr)
{
    ui->setupUi(this);

    this->adjustSize();

    initializeUI();

    // 以当前的初始化绑定为准 大于后续的信号绑定优先级

    calibrationDialog = new CalibrationDialog(this); // 如果calibrationDialog = new CalibrationDialog(nullptr)用于状态栏显示新窗口图标

    connect(calibrationDialog, &CalibrationDialog::signal_readCommand_CalDialog, this, &MainWindow::on_signal_readCommand_CalDialog);
    connect(this, &MainWindow::signal_readCommand_CalDialog_Result, calibrationDialog, &CalibrationDialog::on_signal_readCommand_CalDialog_Result);

    connect(calibrationDialog, &CalibrationDialog::signal_writeCommand_CalDialog, this, &MainWindow::on_signal_writeCommand_CalDialog);
    connect(calibrationDialog, &CalibrationDialog::signal_writeCommand_CalDialog_Reset, this, &MainWindow::on_signal_writeCommand_CalDialog_Reset);   // 618A ntc-32-TIME 不同量程复位地址专用
    connect(this, &MainWindow::signal_writeCommand_CalDialog_Result, calibrationDialog, &CalibrationDialog::on_signal_writeCommand_CalDialog_Result); // 未使用

    connect(calibrationDialog, &CalibrationDialog::signal_startCal_CalDialog, this, &MainWindow::on_signal_startCal_CalDialog);
    connect(this, &MainWindow::signal_startCal_CalDialog_Result, calibrationDialog, &CalibrationDialog::on_signal_startCal_CalDialog_Result); // 未使用

    // 连接电压校准信号
    connect(calibrationDialog, &CalibrationDialog::signal_startVoltageCal_CalDialog, this, &MainWindow::on_signal_startVoltageCal_CalDialog);
    connect(calibrationDialog, &CalibrationDialog::signal_abortVoltageCal_CalDialog, this, &MainWindow::abortVoltageCalibration);

    connect(calibrationDialog->getUi()->historyListWidget, &QListWidget::itemDoubleClicked, this, &MainWindow::onHistoryItemDoubleClicked);
    connect(calibrationDialog->getUi()->historyDelBtn, &QPushButton::clicked, this, &MainWindow::onHistoryDelBtnClicked);
    connect(calibrationDialog->getUi()->restartCal_CalDialog, &QPushButton::clicked, this, &MainWindow::on_recalibrateButton_CalDialog_clicked);
    connect(calibrationDialog->getUi()->endCal_CalDialog, &QPushButton::clicked, this, &MainWindow::on_endCal_CalDialog_clicked);
    connect(calibrationDialog->getUi()->abortButton, &QPushButton::clicked, this, &MainWindow::abortCalibration);
    // 电压校准的中止按钮通过CalibrationDialog的信号处理，不直接连接
    connect(calibrationDialog->getUi()->enterTransparentMode, &QPushButton::clicked, this, &MainWindow::enterTransparentModeClicked);
    connect(calibrationDialog->getUi()->quitTransparentMode, &QPushButton::clicked, this, &MainWindow::quitTransparentModeClicked);

    verificationDialog = new VerificationDialog(this);

    connect(verificationDialog, &VerificationDialog::signal_readCommand_AdjDialog, this, &MainWindow::on_signal_readCommand_AdjDialog);
    connect(this, &MainWindow::signal_readCommand_AdjDialog_Result, verificationDialog, &VerificationDialog::on_signal_readCommand_AdjDialog_Result);
    connect(verificationDialog, &VerificationDialog::signal_startCal_AdjDialog, this, &MainWindow::on_signal_startCal_AdjDialog);

    // 连接VerificationDialog电压校准信号
    connect(verificationDialog, &VerificationDialog::signal_startVoltageCal_AdjDialog, this, &MainWindow::on_signal_startVoltageCal_AdjDialog);
    connect(verificationDialog, &VerificationDialog::signal_abortVoltageCal_AdjDialog, this, &MainWindow::on_signal_abortVoltageCal_AdjDialog);
    connect(verificationDialog, &VerificationDialog::signal_restartVoltageCal_AdjDialog, this, &MainWindow::on_signal_restartVoltageCal_AdjDialog);
    connect(verificationDialog, &VerificationDialog::signal_saveVoltageResults_AdjDialog, this, &MainWindow::on_signal_saveVoltageResults_AdjDialog);

    connect(verificationDialog->getUi()->historyListWidget_Adj, &QListWidget::itemDoubleClicked, this, &MainWindow::onHistoryItemDoubleClicked_Adj);
    verificationDialog->getUi()->historyListWidget_Adj->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(verificationDialog->getUi()->historyListWidget_Adj, &QListWidget::customContextMenuRequested, this, &MainWindow::on_historyListWidget_Adj_customContextMenuRequested);
    connect(verificationDialog->getUi()->historyDelBtn_Adj, &QPushButton::clicked, this, &MainWindow::onHistoryDelBtnClicked_Adj);

    connect(verificationDialog->getUi()->abortCal_Adj, &QPushButton::clicked, this, &MainWindow::abortVerification);
    connect(verificationDialog->getUi()->exportBtn_Adj, &QPushButton::clicked, this, &MainWindow::on_exportBtn_Adj_clicked);
    connect(verificationDialog->getUi()->restartCal_Adj, &QPushButton::clicked, this, &MainWindow::on_recalibrateButton_clicked);
    connect(verificationDialog->getUi()->endCal_Adj, &QPushButton::clicked, this, &MainWindow::on_endCal_Adj_clicked);

    connect(verificationDialog->getUi()->enterTransparentMode_Adj, &QPushButton::clicked, this, &MainWindow::enterTransparentModeClicked_Adj);
    connect(verificationDialog->getUi()->quitTransparentMode_Adj, &QPushButton::clicked, this, &MainWindow::quitTransparentModeClicked_Adj);

    connectToDatabase(); // 连接数据库

    connect(ui->communicationType_Cal, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::onCommunicationType_CalChanged);
    connect(ui->deviceModel_Cal, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::onDeviceModelChanged);

    // 创建处理器实例（每个设备对应一个处理器）
    m_calDeviceSerialHandler->registerProcessor(m_calDeviceProcessor_Serial); // 被校设备专用
    m_calDeviceNetworkHandler->registerProcessor(m_calDeviceProcessor_Network);

    connect(ui->communicationType_1220, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::onCommunicationType_1220Changed);

    m_1220DeviceSerialHandler->registerProcessor(m_1220DeviceProcessor_Serial); // 被校设备专用
    m_1220DeviceNetworkHandler->registerProcessor(m_1220DeviceProcessor_Network);

    // 设置静态实例指针
    s_instance = this;

    // 创建校准线程和工作对象
    calibrationThread = new QThread(this);
    calibrationWorker = new CalibrationWorker();
    calibrationWorker->moveToThread(calibrationThread);

    connect(calibrationThread, &QThread::finished, calibrationWorker, &QObject::deleteLater);

    // 设置命令处理函数
    calibrationWorker->setCommandHandlers(calCommandHandler, deviceCommandHandler);

    // 连接信号和槽
    connect(calibrationWorker, &CalibrationWorker::calibrationStarted,
            this, &MainWindow::handleCalibrationStarted);
    connect(calibrationWorker, &CalibrationWorker::calibrationFinished,
            this, &MainWindow::handleCalibrationFinished);
    connect(calibrationWorker, &CalibrationWorker::calibrationProgress,
            this, &MainWindow::handleCalibrationProgress);
    connect(calibrationWorker, &CalibrationWorker::logMessage,
            this, &MainWindow::handleLogMessage);
    connect(calibrationWorker, &CalibrationWorker::updateChannelData,
            this, &MainWindow::handleUpdateChannelData);
    connect(calibrationWorker, &CalibrationWorker::saveResultsRequested,
            this, &MainWindow::handleSaveResultsRequested);

    // 连接开始校准信号到工作对象的槽
    connect(this, &MainWindow::startCalibration1,
            calibrationWorker, &CalibrationWorker::startCalibration);

    connect(this, &MainWindow::restartCalibration1,
            calibrationWorker, &CalibrationWorker::restartCalibration);

    // 启动线程
    calibrationThread->start();

    // 创建校准线程和工作对象
    verificationThread = new QThread(this);
    verificationWorker = new VerificationWorker();
    verificationWorker->moveToThread(verificationThread);
    connect(verificationThread, &QThread::finished, verificationWorker, &QObject::deleteLater);

    // 设置命令处理函数
    verificationWorker->setCommandHandlers(calCommandHandler, deviceCommandHandler);

    // 连接信号和槽
    connect(verificationWorker, &VerificationWorker::calibrationStarted,
            this, &MainWindow::handleVerificationStarted);
    connect(verificationWorker, &VerificationWorker::calibrationFinished,
            this, &MainWindow::handleVerificationFinished);
    connect(verificationWorker, &VerificationWorker::calibrationProgress,
            this, &MainWindow::handleVerificationProgress);
    connect(verificationWorker, &VerificationWorker::logMessage,
            this, &MainWindow::handleLogMessage_Adj);
    connect(verificationWorker, &VerificationWorker::updateChannelData,
            this, &MainWindow::handleUpdateChannelData_Adj, Qt::QueuedConnection);
    connect(verificationWorker, &VerificationWorker::saveResultsRequested,
            this, &MainWindow::handleSaveResultsRequested_Adj);

    // 连接开始校准信号到工作对象的槽
    // In MainWindow constructor or setup method
    connect(this, &MainWindow::uiUpdateFinished,
            verificationWorker, &VerificationWorker::uiUpdateCompleted,
            Qt::QueuedConnection);

    connect(this, &MainWindow::startCalibration2,
            verificationWorker, &VerificationWorker::startCalibration);

    connect(this, &MainWindow::restartCalibration2,
            verificationWorker, &VerificationWorker::restartCalibration);

    // 启动线程
    verificationThread->start();

    // 创建电压校准线程和工作对象
    voltageCalibrationThread = new QThread(this);
    voltageCalibrationWorker = new VoltageCalibrationWorker();
    voltageCalibrationWorker->moveToThread(voltageCalibrationThread);
    connect(voltageCalibrationThread, &QThread::finished, voltageCalibrationWorker, &QObject::deleteLater);

    // 设置电压校准命令处理函数
    voltageCalibrationWorker->setCommandHandlers(MainWindow::calCommandHandler);

    // 连接电压校准信号和槽
    connect(voltageCalibrationWorker, &VoltageCalibrationWorker::voltageCalibrationFinished,
            this, &MainWindow::handleVoltageCalibrationFinished);
    connect(voltageCalibrationWorker, &VoltageCalibrationWorker::logMessage,
            this, &MainWindow::handleVoltageLogMessage);
    connect(voltageCalibrationWorker, &VoltageCalibrationWorker::updateVoltageData,
            this, &MainWindow::handleUpdateVoltageData);
    connect(voltageCalibrationWorker, &VoltageCalibrationWorker::voltageLevelCompleted,
            this, &MainWindow::handleVoltageLevelCompleted);
    connect(voltageCalibrationWorker, &VoltageCalibrationWorker::requestUserPrompt,
            this, &MainWindow::handleVoltageUserPrompt);
    connect(voltageCalibrationWorker, &VoltageCalibrationWorker::clearVoltageTable,
            this, &MainWindow::initializeVoltageCalibrationTable);
    connect(voltageCalibrationWorker, &VoltageCalibrationWorker::voltageCalibrationProgress,
            this, &MainWindow::handleVoltageCalibrationProgress);

    // 连接开始电压校准信号到工作对象的槽
    connect(this, &MainWindow::startVoltageCalibration,
            voltageCalibrationWorker, &VoltageCalibrationWorker::startVoltageCalibration);

    // 启动电压校准线程
    voltageCalibrationThread->start();

    // 创建1618A TC电压校准线程和工作对象
    tc1618aVoltageVerificationThread = new QThread(this);
    ******************************** = new TC1618AVoltageVerificationWorker();
    ********************************->moveToThread(tc1618aVoltageVerificationThread);
    connect(tc1618aVoltageVerificationThread, &QThread::finished, ********************************, &QObject::deleteLater);

    // 设置1618A TC电压校准命令处理函数
    ********************************->setCommandHandlers(calCommandHandler);

    // 连接1618A TC电压校准信号和槽
    connect(********************************, &TC1618AVoltageVerificationWorker::voltageCalibrationFinished,
            this, &MainWindow::handleVoltageCalibrationFinished_Adj);
    connect(********************************, &TC1618AVoltageVerificationWorker::logMessage,
            this, &MainWindow::handleLogMessage_Adj);
    connect(********************************, &TC1618AVoltageVerificationWorker::updateVoltageTableData,
            this, &MainWindow::handleUpdateVoltageTableData_Adj);
    connect(********************************, &TC1618AVoltageVerificationWorker::requestUserPrompt,
            this, &MainWindow::handleVoltageUserPrompt_Adj);
    connect(********************************, &TC1618AVoltageVerificationWorker::clearVoltageTable,
            this, &MainWindow::generateVoltageAdjTable);
    connect(********************************, &TC1618AVoltageVerificationWorker::voltageCalibrationProgress,
            this, &MainWindow::handleVoltageCalibrationProgress_Adj);

    // 连接开始1618A TC电压校准信号到工作对象的槽
    connect(this, &MainWindow::startTC1618AVoltageCalibration,
            ********************************, &TC1618AVoltageVerificationWorker::startVoltageCalibration);
    connect(this, &MainWindow::abortTC1618AVoltageCalibration,
            ********************************, &TC1618AVoltageVerificationWorker::requestAbort);
    connect(this, &MainWindow::restartTC1618AVoltageCalibration,
            ********************************, &TC1618AVoltageVerificationWorker::startSingleItemRecalibration);

    // 启动1618A TC电压校准线程
    tc1618aVoltageVerificationThread->start();

    // 分批标定worker
    batchCalibrationManager = new BatchCalibrationManager(this);
    isBatchCalibrationInProgress = false;

    connect(batchCalibrationManager, &BatchCalibrationManager::logMessage,
            this, &MainWindow::handleLogMessage);
    connect(batchCalibrationManager, &BatchCalibrationManager::allBatchesCompleted,
            this, &MainWindow::handleBatchCalibrationCompleted);
    connect(batchCalibrationManager, &BatchCalibrationManager::calibrationProgress,
            this, &MainWindow::handleBatchProgress);
    connect(batchCalibrationManager, &BatchCalibrationManager::updateChannelData,
            this, &MainWindow::handleUpdateChannelData);
    connect(batchCalibrationManager, &BatchCalibrationManager::saveResultsRequested,
            this, &MainWindow::handleSaveResultsRequested);
    connect(batchCalibrationManager, &BatchCalibrationManager::reconnectionRequired,
            this, &MainWindow::handleReconnectionRequired);
    connect(batchCalibrationManager, &BatchCalibrationManager::waitingForUserConfirmation,
            this, &MainWindow::handleWaitingForUserConfirmation);
    connect(batchCalibrationManager, &BatchCalibrationManager::batchStarting,
            this, &MainWindow::handleBatchStarting);

    // 分批校准worker
    batchVerificationManager = new BatchVerificationManager(this);
    isBatchVerificationInProgress = false;

    connect(batchVerificationManager, &BatchVerificationManager::logMessage,
            this, &MainWindow::handleLogMessage_Adj);
    connect(batchVerificationManager, &BatchVerificationManager::allBatchesCompleted,
            this, &MainWindow::handleBatchVerificationCompleted);
    connect(batchVerificationManager, &BatchVerificationManager::calibrationProgress,
            this, &MainWindow::handleBatchProgress_Adj);
    connect(batchVerificationManager, &BatchVerificationManager::updateChannelData,
            this, &MainWindow::handleUpdateChannelData_Adj);
    connect(batchVerificationManager, &BatchVerificationManager::saveResultsRequested,
            this, &MainWindow::handleSaveResultsRequested_Adj);
    connect(batchVerificationManager, &BatchVerificationManager::reconnectionRequired,
            this, &MainWindow::handleReconnectionRequired_Adj);
    connect(batchVerificationManager, &BatchVerificationManager::batchStarting,
            this, &MainWindow::handleBatchStarting_Adj);
    connect(this, &MainWindow::uiUpdateFinished, batchVerificationManager, &BatchVerificationManager::onBatchVerificationUiUpdateCompleted);
}

MainWindow::~MainWindow()
{
    // First disconnect all signals/slots
    disconnect();

    // Close and delete dialogs
    if (calibrationDialog)
    {
        calibrationDialog->close();
        calibrationDialog->deleteLater();
        calibrationDialog = nullptr;
    }

    progressDialog = nullptr;

    if (projectDetailsDialog)
    {
        projectDetailsDialog->close();
        projectDetailsDialog->deleteLater();
        projectDetailsDialog = nullptr;
    }

    if (verificationDialog)
    {
        verificationDialog->close();
        verificationDialog->deleteLater();
        verificationDialog = nullptr;
    }

    // Delete handlers and processors with null checks
    if (dbHandler)
    {
        delete dbHandler;
        dbHandler = nullptr;
    }

    if (m_calDeviceProcessor_Serial)
    {
        delete m_calDeviceProcessor_Serial;
        m_calDeviceProcessor_Serial = nullptr;
    }

    if (m_calDeviceProcessor_Network)
    {
        delete m_calDeviceProcessor_Network;
        m_calDeviceProcessor_Network = nullptr;
    }

    if (m_calDeviceSerialHandler)
    {
        delete m_calDeviceSerialHandler;
        m_calDeviceSerialHandler = nullptr;
    }

    if (m_calDeviceNetworkHandler)
    {
        delete m_calDeviceNetworkHandler;
        m_calDeviceNetworkHandler = nullptr;
    }

    if (m_1220DeviceProcessor_Serial)
    {
        delete m_1220DeviceProcessor_Serial;
        m_1220DeviceProcessor_Serial = nullptr;
    }

    if (m_1220DeviceProcessor_Network)
    {
        delete m_1220DeviceProcessor_Network;
        m_1220DeviceProcessor_Network = nullptr;
    }

    if (m_1220DeviceSerialHandler)
    {
        delete m_1220DeviceSerialHandler;
        m_1220DeviceSerialHandler = nullptr;
    }

    if (m_1220DeviceNetworkHandler)
    {
        delete m_1220DeviceNetworkHandler;
        m_1220DeviceNetworkHandler = nullptr;
    }

    // Stop and clean up threads first
    if (calibrationThread)
    {
        calibrationThread->quit();
        if (!calibrationThread->wait(500))
        {
            qWarning() << "Warning: Calibration thread did not finish in time";
            calibrationThread->terminate();
            calibrationThread->wait();
        }
        delete calibrationThread;
        calibrationThread = nullptr;
    }
    calibrationWorker = nullptr;

    if (verificationThread)
    {
        verificationThread->quit();
        if (!verificationThread->wait(500))
        {
            qWarning() << "Warning: Verification thread did not finish in time";
            verificationThread->terminate();
            verificationThread->wait();
        }
        delete verificationThread;
        verificationThread = nullptr;
    }
    verificationWorker = nullptr;

    if (voltageCalibrationThread)
    {
        voltageCalibrationThread->quit();
        if (!voltageCalibrationThread->wait(500))
        {
            qWarning() << "Warning: Voltage calibration thread did not finish in time";
            voltageCalibrationThread->terminate();
            voltageCalibrationThread->wait();
        }
        delete voltageCalibrationThread;
        voltageCalibrationThread = nullptr;
    }
    voltageCalibrationWorker = nullptr;

    // Clear static instance
    if (s_instance == this)
    {
        s_instance = nullptr;
    }

    // Delete UI last
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void MainWindow::connectToDatabase()
{
    QString dbPath = QDir(QCoreApplication::applicationDirPath()).filePath("Auto_TCal_DB.db");
    QFileInfo checkFile(dbPath);

    if (!checkFile.isFile())
    {
        QMessageBox::critical(nullptr, "Error", "Database file does not exist at: " + dbPath);
        return;
    }

    qDebug() << "dbPath:" << dbPath;

    if (dbHandler->connectToDatabase(dbPath))
    {
        loadCalibrationHistory();
        loadCalibrationHistory_Adj();
    }
}

void MainWindow::on_connectButton_Cal_clicked()
{
    // 检查透传模式状态
    if (!checkTransparentModeBeforeOperation("连接设备"))
    {
        return;
    }

    if (ui->communicationType_Cal->currentIndex() == 0)
    {
        // 串口连接
        QString portName = ui->portNum_Cal->currentText();
        int baudRate = ui->baudNum_Cal->currentText().toInt();

        auto result = m_calDeviceSerialHandler->openPort(portName, baudRate);
        if (result)
        {
            ui->disconnectButton_Cal->setEnabled(true);
            ui->connectButton_Cal->setEnabled(false);
            resetTransparentModeState(); // 连接时重置透传状态
            QMessageBox::information(this, "提示", "串口打开成功");
        }
        else
        {
            QMessageBox::warning(this, "警告", "无法打开串口");
        }
    }
    else
    {
        // 网络连接
        QString host = ui->ipAddress_Cal->text().trimmed();
        QString portText = ui->ipPort_Cal->text().trimmed();

        // 验证IP地址
        QHostAddress address;
        if (host.isEmpty() || !address.setAddress(host))
        {
            QMessageBox::warning(this, "警告", "请输入有效的IP地址！");
            return;
        }

        // 验证端口号
        bool ok;
        int port = portText.toInt(&ok);
        if (!ok || port <= 0 || port > 65535)
        {
            QMessageBox::warning(this, "警告", "请输入有效的端口号(1-65535)！");
            return;
        }

        // 使用新的返回值处理方式
        QPair<bool, QString> result = m_calDeviceNetworkHandler->connectToHost(host, port);
        if (result.first)
        {
            ui->disconnectButton_Cal->setEnabled(true);
            ui->connectButton_Cal->setEnabled(false);
            resetTransparentModeState(); // 连接时重置透传状态
            QMessageBox::information(this, "提示", "网络连接成功");
        }
        else
        {
            QMessageBox::warning(this, "警告", "网络连接失败: " + result.second);
        }
    }
}

void MainWindow::on_disconnectButton_Cal_clicked()
{
    // 检查透传模式状态
    if (!checkTransparentModeBeforeOperation("断开设备连接"))
    {
        return;
    }

    if (ui->communicationType_Cal->currentIndex() == 0)
    {
        if (m_calDeviceSerialHandler->closePort())
        {
            ui->disconnectButton_Cal->setEnabled(false);
            ui->connectButton_Cal->setEnabled(true);
            resetTransparentModeState(); // 断开连接时重置透传状态
            QMessageBox::information(this, "提示", "串口已关闭");
        }
    }
    else
    {
        if (m_calDeviceNetworkHandler->disconnectFromHost())
        {
            ui->disconnectButton_Cal->setEnabled(false);
            ui->connectButton_Cal->setEnabled(true);
            resetTransparentModeState(); // 断开连接时重置透传状态
            QMessageBox::information(this, "提示", "网络已断开");
        }
    }
}

void MainWindow::on_connectButton_1220_clicked()
{
    if (ui->communicationType_1220->currentIndex() == 0)
    {
        // 串口连接
        QString portName = ui->portNum_1220->currentText();
        int baudRate = ui->baudNum_1220->currentText().toInt();

        auto result = m_1220DeviceSerialHandler->openPort(portName, baudRate);
        if (result)
        {
            ui->disconnectButton_1220->setEnabled(true);
            ui->connectButton_1220->setEnabled(false);
            QMessageBox::information(this, "提示", "串口打开成功");
        }
        else
        {
            QMessageBox::warning(this, "警告", "无法打开串口");
        }
    }
    else
    {
        // 网络连接
        QString host = ui->ipAddress_1220->text().trimmed();
        QString portText = ui->ipPort_1220->text().trimmed();

        // 验证IP地址
        QHostAddress address;
        if (host.isEmpty() || !address.setAddress(host))
        {
            QMessageBox::warning(this, "警告", "请输入有效的IP地址！");
            return;
        }

        // 验证端口号
        bool ok;
        int port = portText.toInt(&ok);
        if (!ok || port <= 0 || port > 65535)
        {
            QMessageBox::warning(this, "警告", "请输入有效的端口号(1-65535)！");
            return;
        }

        // 使用新的返回值处理方式
        QPair<bool, QString> result = m_1220DeviceNetworkHandler->connectToHost(host, port);
        if (result.first)
        {
            ui->disconnectButton_1220->setEnabled(true);
            ui->connectButton_1220->setEnabled(false);
            QMessageBox::information(this, "提示", "网络连接成功");
        }
        else
        {
            QMessageBox::warning(this, "警告", "网络连接失败: " + result.second);
        }
    }
}

void MainWindow::on_disconnectButton_1220_clicked()
{
    if (ui->communicationType_1220->currentIndex() == 0)
    {
        if (m_1220DeviceSerialHandler->closePort())
        {
            ui->disconnectButton_1220->setEnabled(false);
            ui->connectButton_1220->setEnabled(true);
            QMessageBox::information(this, "提示", "串口已关闭");
        }
    }
    else
    {
        if (m_1220DeviceNetworkHandler->disconnectFromHost())
        {
            ui->disconnectButton_1220->setEnabled(false);
            ui->connectButton_1220->setEnabled(true);
            QMessageBox::information(this, "提示", "网络已断开");
        }
    }
}

QPair<bool, QString> MainWindow::sendCommand_Cal(const QByteArray &data, const QString &command)
{
    if (ui->communicationType_Cal->currentIndex() == 0)
    {
        return m_calDeviceSerialHandler->writeData(data, command);
    }
    else
    {
        return m_calDeviceNetworkHandler->writeData(data, command);
    }
}

QPair<bool, QString> MainWindow::sendCommand_1220(const QByteArray &data, const QString &command)
{
    if (ui->communicationType_1220->currentIndex() == 0)
    {
        return m_1220DeviceSerialHandler->writeData(data, command);
    }
    else
    {
        return m_1220DeviceNetworkHandler->writeData(data, command);
    }
}

// 静态命令处理函数实现
QPair<bool, QString> MainWindow::calCommandHandler(const QByteArray &command, const QString &commandType)
{
    if (s_instance)
    {
        return s_instance->sendCommand_Cal(command, commandType);
    }
    return {false, "主窗口实例不存在"};
}

QPair<bool, QString> MainWindow::deviceCommandHandler(const QByteArray &command, const QString &commandType)
{
    if (s_instance)
    {
        return s_instance->sendCommand_1220(command, commandType);
    }
    return {false, "主窗口实例不存在"};
}

// /*-----------------------------------标定---------------------------------------*/

double getReferRValue(QLineEdit *lineEdit, int refIndex, int &deviceRefIndex)
{
    if (lineEdit->text().isEmpty())
    {
        QMessageBox::warning(nullptr, "警告", "请输入有效参考电阻值");
        return -1;
    }
    deviceRefIndex = refIndex;
    return lineEdit->text().toDouble();
}

void MainWindow::on_signal_startCal_CalDialog(const QString &deviceModel, const QString &featureCode, const int channelNums, const QString &referRVal)
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    if (featureCode == "startCal")
    { // 传参：设备型号、功能码、通道个数、参考N
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "开始标定"))
        {
            return;
        }

        // 1、写入外部测量值 2、读取多通道阻值并返回
        CalDeviceConfig device;
        device.name = deviceModel; // 特殊设备型号：1618A-N/1618A-L 从CalibrationDialog传入的格式为拼接字符：1618A-N-NTC-01或1618A-L-NTC-01（类型-精度）
        device.num_channels = channelNums;

        QMap<QString, DeviceFirstAddress> deviceFirstAddressConfig;
        deviceFirstAddressConfig["618A RTD"] = {0X0225, 0X0059};
        deviceFirstAddressConfig["618A RTD PLUS"] = {0X0225, 0X0059};
        deviceFirstAddressConfig["619A RTD PLUS"] = {0X0225, 0X0059};
        QStringList _618NTCModels = {"618A", "618A PLUS", "618A PLUS(6NTC+2P-AP23)", "619A PLUS", "TM14RD-PT100", "TM14RD-PT1000", "TM14ND", "TM14ND-T", "TM14ND-P", "TM14ND-P-S", "H-LCW-22B"};
        for (const QString &model : _618NTCModels)
        {
            deviceFirstAddressConfig[model] = {0X01A5, 0X0059};
        }
        deviceFirstAddressConfig["618A NTC-32"] = {0X0325, 0X0099};
        // deviceFirstAddressConfig["618A NTC-32-TIME"] = {0X0325, 0X00D9};
        deviceFirstAddressConfig["619A NTC-32 PLUS"] = {0X0325, 0X0099};
        deviceFirstAddressConfig["619A RTD-32 PLUS"] = {0X0425, 0X0099};

        deviceFirstAddressConfig["TM18ND-P"] = {0X014D, 0X0061};
        deviceFirstAddressConfig["TM18RD-P"] = {0X014D, 0X0061};

        deviceFirstAddressConfig["1611A-HT(PT100)"] = {0X0152, 0X0034};
        deviceFirstAddressConfig["1611A-HT(PT1000)"] = {0X0152, 0X0034};

        deviceFirstAddressConfig["TM24ND-P-S"] = {0X01A4, 0X0058};

        // if (deviceFirstAddressConfig.contains(deviceModel)) {
        //     device.write_addr = deviceFirstAddressConfig[deviceModel].write_addr;
        //     device.read_addr = deviceFirstAddressConfig[deviceModel].read_addr;
        // }

        /*
        if(deviceModel == "618A-RTD" || deviceModel == "618A-PLUS-RTD" || deviceModel == "619A-PLUS-RTD") {
            device.write_addr = 0X0225;
            device.read_addr = 0X0059;
        } else {
            device.write_addr = 0X01A5;
            device.read_addr = 0X0059;
        }*/

        QVector<QLineEdit *> refInputs = {ui->referRVal1, ui->referRVal2, ui->referRVal3, ui->referRVal4, ui->referRVal5};
        QVector<QCheckBox *> refChecks = {ui->refer1, ui->refer2, ui->refer3, ui->refer4, ui->refer5};
        QVector<QComboBox *> refChannels = {ui->referCH1, ui->referCH2, ui->referCH3, ui->referCH4, ui->referCH5};

        for (int i = 0; i < refChecks.size(); ++i)
        {
            if (refChecks[i]->isChecked())
            {
            }
        }

        double referRValue;
        if (referRVal == "参考1")
        {
            int channelIndex = ui->referCH1->currentIndex() + 1;                          // 索引从1开始
            referRValue = getReferRValue(ui->referRVal1, channelIndex, device.ref_index); // 获取referRVal1参考1的阻值 并把device.ref_index设置为当前参考N选择的通道
        }
        else if (referRVal == "参考2")
        {
            int channelIndex = ui->referCH2->currentIndex() + 1; // 索引从1开始
            referRValue = getReferRValue(ui->referRVal2, channelIndex, device.ref_index);
        }
        else if (referRVal == "参考3")
        {
            int channelIndex = ui->referCH3->currentIndex() + 1; // 索引从1开始
            referRValue = getReferRValue(ui->referRVal3, channelIndex, device.ref_index);
        }
        else if (referRVal == "参考4")
        {
            int channelIndex = ui->referCH4->currentIndex() + 1; // 索引从1开始
            referRValue = getReferRValue(ui->referRVal4, channelIndex, device.ref_index);
        }
        else
        {
            int channelIndex = ui->referCH5->currentIndex() + 1; // 索引从1开始
            referRValue = getReferRValue(ui->referRVal5, channelIndex, device.ref_index);
        }

        if (referRValue == -1)
        {
            return; // 输入无效，已显示警告消息
        }

        device.ref_values = QVector<double>(device.num_channels, referRValue);

        // 设置开关切换等待时间，从UI获取（假设已存在ui->switchDelay组合框）
        device.switchDelay = ui->switchDelay ? ui->switchDelay->currentText().toInt() : 30; // 默认30秒

        // "618A NTC-32-TIME" 有需要标定4个挡位 每个挡位写入外部测量值首地址不同
        if (deviceModel == "618A NTC-32-TIME")
        {
            device.read_addr = 0x00D9;

            struct RangeAddr
            {
                double minR;
                double maxR;
                uint16_t writeAddr;
            };

            const RangeAddr ranges[] = {
                // {900, 1100, 0x0325},    // 1k range
                // {4500, 5500, 0x0365},   // 5k range
                // {9500, 10500, 0x03A5},  // 10k range
                // {19000, 21000, 0x03E5}  // 20k range
                {900, 1100, 0x03E5},   // 1k range
                {4500, 5500, 0x03A5},  // 5k range
                {9500, 10500, 0x0365}, // 10k range
                {19000, 21000, 0x0325} // 20k range
            };

            bool matched = false;
            for (const auto &range : ranges)
            {
                if (referRValue >= range.minR && referRValue <= range.maxR)
                {
                    device.write_addr = range.writeAddr;
                    matched = true;
                    break;
                }
            }

            if (!matched)
            {
                device.write_addr = 0x0325; // 默认1k范围
                qWarning() << "Warning: Resistance value" << referRValue
                           << "for 618A NTC-32-TIME doesn't fall into any standard range. Using 1k range address.";
            }
        }
        else if (ui->deviceModel_Cal->currentText() == "1618A-N" || ui->deviceModel_Cal->currentText() == "1618A-L")
        { // 1618A-N和1618A-L的板卡读写地址需要根据板卡类型和参考电阻确定
            // 1618A-L和1618A-N使用不同的地址
            if (ui->deviceModel_Cal->currentText() == "1618A-L")
            {
                device.read_addr = 0X015D; // 1618A-L读取地址

                // 检查是否为RTD板卡
                if (deviceModel.contains("-RTD-"))
                {
                    // RTD板卡需要根据参考电阻值确定写入地址
                    if (qAbs(referRValue - 1000.0) < 500.0)
                    {
                        device.write_addr = 0X00F9; // 1618A-L RTD 参考1：1kΩ
                    }
                    else if (qAbs(referRValue - 100.0) < 50.0)
                    {
                        device.write_addr = 0X0119; // 1618A-L RTD 参考2：100Ω
                    }
                    else
                    {
                        device.write_addr = 0X00F9; // 默认使用参考1地址
                    }
                }
                else
                {
                    // 1618A-L NTC板卡使用固定地址
                    device.write_addr = 0X00F9;
                }
            }
            else // 1618A-N
            {
                device.read_addr = 0X003D; // 1618A-N读取地址固定

                // 检查是否为RTD板卡
                if (deviceModel.contains("-RTD-"))
                {
                    // RTD板卡需要根据参考电阻值确定写入地址
                    if (qAbs(referRValue - 1000.0) < 500.0)
                    {
                        device.write_addr = 0X0441; // 1618A-N RTD 参考1：1kΩ
                    }
                    else if (qAbs(referRValue - 100.0) < 50.0)
                    {
                        device.write_addr = 0X0461; // 1618A-N RTD 参考2：100Ω
                    }
                    else
                    {
                        device.write_addr = 0X0441; // 默认使用参考1地址
                    }
                }
                else
                {
                    // 1618A-N NTC板卡使用固定地址
                    device.write_addr = 0X0441;
                }
            }
        }
        else if (deviceFirstAddressConfig.contains(deviceModel))
        {
            device.write_addr = deviceFirstAddressConfig[deviceModel].write_addr;
            device.read_addr = deviceFirstAddressConfig[deviceModel].read_addr;
        }

        // Only calculate cal_to_1220 mappings if not using batch calibration
        const int thresholdForBatchCalibration = 16;
        if (device.num_channels <= thresholdForBatchCalibration)
        {
            // 8 -- {13, 14, 15, 16, 17, 18, 19, 20}
            device.cal_to_1220.resize(device.num_channels);
            int start_value = 21 - device.num_channels;

            for (int i = 0; i < device.num_channels; ++i)
            {
                device.cal_to_1220[i] = start_value + i;
            }
        }
        else
        {
            // 设置默认值
            device.cal_to_1220 = QVector<int>(device.num_channels, -1); // 使用 -1 作为默认值
        }

        // 当超出16通道数后 cal_to_1220值会在分批校准worker中各自分配
        startCalibrationInThread(device);
    }
    else if (featureCode == "endCal")
    {
        // abortCalibration(); //直接在mainwindow中连接信号
    }
}

void MainWindow::on_signal_readCommand_CalDialog(const QString &deviceModel, const QString &featureCode)
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    QPair<bool, QString> result;

    if (featureCode == "readNum")
    {
        handleLogMessage("开始读取设备序列号...");

        // 检查是否为透传模式设备
        if (transparentModeDevices.contains(deviceModel))
        {
            if (isWakeUp)
            {
                // 检查设备型号是否一致
                if (currentTransparentDevice != deviceModel)
                {
                    handleLogMessage("检测到设备型号不一致，自动重置透传模式状态");
                    resetTransparentModeState();
                }
                else
                {
                    // 已唤醒状态：不允许读写序列号
                    handleLogMessage("设备已进入透传模式，不能读取序列号");
                    QMessageBox::warning(calibrationDialog, "提示", "设备已进入透传模式，请先退出透传模式再读取序列号！");
                    return;
                }
            }

            // 未唤醒状态：使用字符串格式读取序列号
            handleLogMessage("使用字符串格式读取设备序列号...");
            QString msg = "SYST:SERIAL?\r\n";
            QByteArray sendData = msg.toLatin1();
            result = sendCommand_Cal(sendData, "Cal_ReadNum_String");
            if (result.first)
            {
                handleLogMessage("读取设备序列号成功: " + result.second);
                emit signal_readCommand_CalDialog_Result(result.second, "readNum");
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取序列号失败：" + result.second;
                handleLogMessage(errorMsg);
                QMessageBox::critical(calibrationDialog, "错误", errorMsg);
            }
        }
        else if (deviceModel == "ZCLOG 334NTC")
        {
            // QByteArray sendData = QByteArray::fromHex("FEFEFEFE013C0003"); // 读取设备序列号
            // sendCommand_Cal(sendData, "Cal_ReadNum");
        }
        else
        {
            // TM24ND-P-S使用不同的序列号地址
            QString serialAddr = (deviceModel == "TM24ND-P-S") ? "00 10 00 05" : "00 11 00 05";
            QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + serialAddr);
            result = sendCommand_Cal(sendData, "Cal_ReadNum");
            if (result.first)
            {
                handleLogMessage("读取设备序列号成功: " + result.second);
                emit signal_readCommand_CalDialog_Result(result.second, "readNum");
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取序列号失败：" + result.second;
                handleLogMessage(errorMsg);
                QMessageBox::critical(calibrationDialog, "错误", errorMsg);
            }
        }
    }
    else if (featureCode == "readFilterNum")
    {
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "读取滤波次数"))
        {
            return;
        }

        handleLogMessage("开始读取滤波次数...");
        // 根据设备型号确定滤波次数地址
        QString filterAddr;
        if (deviceModel == "1611A-HT(PT100)" || deviceModel == "1611A-HT(PT1000)")
        {
            filterAddr = "00 03 00 01";
        }
        else if (deviceModel == "TM24ND-P-S")
        {
            filterAddr = "00 09 00 01";
        }
        else
        {
            filterAddr = "00 0A 00 01";
        }
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + filterAddr);
        // 为TM24ND-P-S使用专门的命令标识符
        QString commandId = (deviceModel == "TM24ND-P-S") ? "Cal_ReadFilterNum_TM24ND" : "Cal_ReadFilterNum";
        result = sendCommand_Cal(sendData, commandId);
        if (result.first)
        {
            handleLogMessage("读取滤波次数成功: " + result.second);
            emit signal_readCommand_CalDialog_Result(result.second, "readFilterNum");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取滤波次数失败：" + result.second;
            handleLogMessage(errorMsg);
            QMessageBox::critical(calibrationDialog, "错误", errorMsg);
        }
    }
    else if (featureCode == "readFilterNum1618A")
    {
        // 检查1618A-L是否需要透传模式
        QString deviceModel = calibrationDialog->getUi()->deviceModel_CalDialog->text();
        if (deviceModel == "1618A-L")
        {
            if (!checkTransparentModeRequirement(deviceModel, "读取滤波次数"))
            {
                return;
            }
        }

        handleLogMessage("开始读取1618A滤波次数...");
        // 1618A-L和1618A-N使用不同的滤波次数地址
        QString filterAddr = (deviceModel == "1618A-L") ? "00 0D 00 01" : "03 55 00 01";
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + filterAddr);
        result = sendCommand_Cal(sendData, "Cal_ReadFilterNum1618A");
        if (result.first)
        {
            handleLogMessage("读取1618A滤波次数成功: " + result.second);
            emit signal_readCommand_CalDialog_Result(result.second, "readFilterNum1618A");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取1618A滤波次数失败：" + result.second;
            handleLogMessage(errorMsg);
            QMessageBox::critical(calibrationDialog, "错误", errorMsg);
        }
    }
    else if (featureCode == "readBaudRate")
    {
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "读取波特率"))
        {
            return;
        }

        handleLogMessage("开始读取波特率...");
        // TM24ND-P-S使用不同的波特率地址
        QString baudRateAddr = (deviceModel == "TM24ND-P-S") ? "00 01 00 01" : "00 02 00 01";
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + baudRateAddr);
        // 为TM24ND-P-S使用专门的命令标识符
        QString commandId = (deviceModel == "TM24ND-P-S") ? "Cal_ReadBaudRate_TM24ND" : "Cal_ReadBaudRate";
        result = sendCommand_Cal(sendData, commandId);
        if (result.first)
        {
            handleLogMessage("读取波特率成功: " + result.second);
            QMessageBox::information(calibrationDialog, "波特率", "当前波特率值: " + result.second);
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取波特率失败：" + result.second;
            handleLogMessage(errorMsg);
            QMessageBox::critical(calibrationDialog, "错误", errorMsg);
        }
    }
    else if (featureCode == "readBoardCardNumber")
    { // 读取板卡编号
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "读取板卡编号"))
        {
            return;
        }

        handleLogMessage("开始读取板卡编号...");
        // 1618A-L和1618A-N使用不同的板卡编号地址
        QString boardCardAddr = (deviceModel == "1618A-L") ? "00 04 00 05" : "03 4C 00 05";
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + boardCardAddr);
        result = sendCommand_Cal(sendData, "Cal_ReadBoardCardNumber");
        if (result.first)
        {
            handleLogMessage("读取设备板卡编号成功: " + result.second);
            emit signal_readCommand_CalDialog_Result(result.second, "readBoardCardNumber");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取设备板卡编号失败：" + result.second;
            handleLogMessage(errorMsg);
            QMessageBox::critical(calibrationDialog, "错误", errorMsg);
        }
    }
    else if (featureCode == "readBoardCardModel")
    { // 读取板卡精度
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "读取板卡精度"))
        {
            return;
        }

        handleLogMessage("开始读取板卡精度...");
        // 1618A-L和1618A-N使用不同的板卡精度地址
        QString boardModelAddr = (deviceModel == "1618A-L") ? "00 02 00 01" : "03 4A 00 01";
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + boardModelAddr);
        result = sendCommand_Cal(sendData, "Cal_ReadBoardCardModel");
        if (result.first)
        {
            handleLogMessage("读取设备板卡精度成功: " + result.second);
            emit signal_readCommand_CalDialog_Result(result.second, "readBoardCardModel");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取板卡精度失败：" + result.second;
            handleLogMessage(errorMsg);
            QMessageBox::critical(calibrationDialog, "错误", errorMsg);
        }
    }
}

QString getResetAddr(const QString &deviceModel)
{
    // 创建设备型号与地址的映射
    QMap<QString, QString> resetAddrMap = {
        {"618A", "01 9B"},
        {"618A PLUS", "01 9B"},
        {"618A PLUS(6NTC+2P-AP23)", "01 9B"},
        {"619A PLUS", "01 9B"},

        {"618A RTD", "02 1B"},
        {"618A RTD PLUS", "02 1B"},
        {"619A RTD PLUS", "02 1B"},

        {"TM14RD-PT100", "01 9B"},
        {"TM14RD-PT1000", "01 9B"},
        {"TM14ND", "01 9B"},
        {"TM14ND-T", "01 9B"},
        {"TM14ND-P", "01 9B"},
        {"TM14ND-P-S", "01 9B"},

        {"TM18ND-P", "01 43"},
        {"TM18RD-P", "01 43"},
        {"TM24ND-P-S", "01 9A"},

        {"H-LCW-22B", "01 9B"},

        {"TM222ND-P", "01 9B"},
        {"TM224ND-P", "01 9B"},
        {"TM228ND-P", "01 9B"},

        {"618A NTC-32", "03 1B"},
        {"618A NTC-32-TIME", "03 1B"},
        {"619A NTC-32 PLUS", "03 1B"},
        {"619A RTD-32 PLUS", "04 1B"},

        {"1618A-N", "04 3D"},
        {"1618A-L", "00 F5"},

        {"1611A-HT(PT100)", "00 00"}, // 1611A-HT无复位
        {"1611A-HT(PT1000)", "00 00"},
    };

    // 查找设备型号对应的地址
    if (resetAddrMap.contains(deviceModel))
    {
        return resetAddrMap[deviceModel];
    }
    else
    {
        return "01 9B";
    }
}

void MainWindow::on_signal_writeCommand_CalDialog_Reset(const QString &deviceModel, const QString &featureCode, const QString &paramVal, const QString &referRVal)
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    QPair<bool, QString> result;
    int charCount = paramVal.length();
    QString sum = QString("%1").arg(charCount, 2, 16, QChar('0')).toUpper();

    QByteArray asciiData;
    for (const QChar &c : paramVal)
    {
        asciiData.append(c.toLatin1());
    }

    // 获取参考电阻值
    double referRValue = 0.0;
    if (referRVal == "参考1")
    {
        referRValue = ui->referRVal1->text().toDouble(); // 获取referRVal1参考1的阻值
    }
    else if (referRVal == "参考2")
    {
        referRValue = ui->referRVal2->text().toDouble();
    }
    else if (referRVal == "参考3")
    {
        referRValue = ui->referRVal3->text().toDouble();
    }
    else if (referRVal == "参考4")
    {
        referRValue = ui->referRVal4->text().toDouble();
    }
    else
    {
        referRValue = ui->referRVal5->text().toDouble();
    }

    QString m_resetWriteVal;

    if (deviceModel == "618A NTC-32-TIME")
    {
        // 解析原始通道号（从paramVal中获取）
        bool ok;
        int channelNumber = paramVal.split(" ")[0].toInt(&ok, 16);

        if (!ok)
        {
            handleLogMessage("错误：无法解析通道号");
            return;
        }

        // 定义电阻范围及对应的偏移量
        struct RangeOffset
        {
            double minR;       // 最小电阻值
            double maxR;       // 最大电阻值
            int offset;        // 偏移量
            QString rangeName; // 范围名称（用于日志）
        };

        // 定义不同挡位的电阻范围和对应偏移值
        // const RangeOffset ranges[] = {
        //     {900, 1100, 0, "1k"},       // 1k挡位 - 通道1-32
        //     {4500, 5500, 32, "5k"},     // 5k挡位 - 通道33-64
        //     {9500, 10500, 64, "10k"},   // 10k挡位 - 通道65-96
        //     {19000, 21000, 96, "20k"}   // 20k挡位 - 通道97-128
        // };
        const RangeOffset ranges[] = {
            {19000, 21000, 0, "20k"}, // 20k挡位 - 通道1-32
            {9500, 10500, 32, "10k"}, // 10k挡位 - 通道33-64
            {4500, 5500, 64, "5k"},   // 5k挡位 - 通道65-96
            {900, 1100, 96, "1k"}     // 1k挡位 - 通道97-128
        };

        // 默认使用第一段量程（1k）
        int offset = 0;
        QString rangeName = "1k";

        // 查找匹配的范围
        for (const auto &range : ranges)
        {
            if (referRValue >= range.minR && referRValue <= range.maxR)
            {
                offset = range.offset;
                rangeName = range.rangeName;
                break;
            }
        }

        // 计算实际的复位值（通道号+偏移量）
        int resetValue = channelNumber + offset;

        // 确保resetValue不超过128
        if (resetValue > 128)
        {
            handleLogMessage(QString("警告：计算的复位值 %1 超出范围，已调整为最大值 128").arg(resetValue));
            resetValue = 128;
        }

        // 格式化为两个字节的十六进制字符串
        m_resetWriteVal = QString("%1 00").arg(resetValue, 2, 16, QChar('0')).toUpper();
    }
    else
    {
        // 非618A NTC-32-TIME型号，使用原始参数值
        m_resetWriteVal = paramVal;
    }

    if (featureCode == "writeChannelRest")
    {
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "执行通道复位"))
        {
            return;
        }

        handleLogMessage("开始通道复位...");

        QString m_resetAddr;
        m_resetAddr = getResetAddr(deviceModel);

        // 对TM24ND-P-S的复位值进行大端处理
        QString resetWriteVal = m_resetWriteVal;
        if (deviceModel == "TM24ND-P-S")
        {
            // TM24ND-P-S需要大端格式，例如：01 00 -> 00 01
            QStringList parts = resetWriteVal.trimmed().split(' ');
            if (parts.size() == 2)
            {
                QString highByte = parts[1];                    // 原来的高字节
                QString lowByte = parts[0];                     // 原来的低字节
                resetWriteVal = " " + highByte + " " + lowByte; // 大端格式：高字节在前
            }
        }

        QByteArray sendData = createModbusCommand(m_globalHeadStr + "06" + m_resetAddr + resetWriteVal);
        // 为TM24ND-P-S使用专门的命令标识符（如果需要特殊处理）
        QString commandId = (deviceModel == "TM24ND-P-S") ? "Cal_WriteChannelRest_TM24ND" : "Cal_WriteChannelRest";
        result = sendCommand_Cal(sendData, commandId);
        ;
        if (result.first)
        {
            handleLogMessage("当前通道复位成功！");
            QMessageBox::information(calibrationDialog, "成功", "当前通道复位成功！");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "当前通道复位失败：" + result.second;
            handleLogMessage(errorMsg);
            QMessageBox::critical(calibrationDialog, "错误", errorMsg);
        }
    }
}

void MainWindow::on_signal_writeCommand_CalDialog(const QString &deviceModel, const QString &featureCode, const QString &paramVal)
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    QPair<bool, QString> result;
    int charCount = paramVal.length();
    QString sum = QString("%1").arg(charCount, 2, 16, QChar('0')).toUpper();

    QByteArray asciiData;
    for (const QChar &c : paramVal)
    {
        asciiData.append(c.toLatin1());
    }

    if (featureCode == "writeNum")
    {
        handleLogMessage("开始写入设备序列号...");

        // 检查是否为透传模式设备
        if (transparentModeDevices.contains(deviceModel))
        {
            if (isWakeUp)
            {
                // 检查设备型号是否一致
                if (currentTransparentDevice != deviceModel)
                {
                    handleLogMessage("检测到设备型号不一致，自动重置透传模式状态");
                    resetTransparentModeState();
                }
                else
                {
                    // 已唤醒状态：不允许读写序列号
                    handleLogMessage("设备已进入透传模式，不能写入序列号");
                    QMessageBox::warning(calibrationDialog, "提示", "设备已进入透传模式，请先退出透传模式再写入序列号！");
                    return;
                }
            }

            // 未唤醒状态：使用字符串格式写入序列号
            handleLogMessage("使用字符串格式写入设备序列号...");
            QString msg = "SYST:SERIAL " + paramVal + "\r\n";
            QByteArray sendData = msg.toLatin1();
            result = sendCommand_Cal(sendData, "Cal_WriteNum_String");
            if (result.first)
            {
                // 检查返回内容是否表示成功
                if (result.second.contains("写入序列号成功"))
                {
                    handleLogMessage("写入设备序列号成功: " + result.second);
                    QMessageBox::information(calibrationDialog, "成功", "写入序列号成功！");
                }
                else
                {
                    handleLogMessage("写入设备序列号失败: " + result.second);
                    QMessageBox::critical(calibrationDialog, "错误", "写入序列号失败：" + result.second);
                }
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "写入序列号失败：" + result.second;
                handleLogMessage(errorMsg);
                QMessageBox::critical(calibrationDialog, "错误", errorMsg);
            }
        }
        else if (deviceModel == "ZCLOG 334NTC")
        {
            QString commandString = "FE FE FE FE 01 3B 08 " + asciiData.toHex(' ') + " 03";
            QString cleanCommand = commandString.simplified().remove(' ');
            QByteArray sendData = QByteArray::fromHex(cleanCommand.toLatin1());
            sendCommand_Cal(sendData, "Cal_WriteNum");
        }
        else
        {
            // 1618A-L、TM24ND-P-S和其他设备使用不同的序列号地址
            QString serialAddr;
            if (deviceModel == "1618A-L")
            {
                serialAddr = "00 11 00 05"; // 1618A-L使用标准地址（与1618A-N相同）
            }
            else if (deviceModel == "TM24ND-P-S")
            {
                serialAddr = "00 10 00 05"; // TM24ND-P-S使用特殊地址
            }
            else
            {
                serialAddr = "00 11 00 05"; // 其他设备使用标准地址
            }

            QByteArray sendData = createModbusCommand(m_globalHeadStr + "0E" + serialAddr + sum + " " + asciiData.toHex(' '));
            // 为TM24ND-P-S使用专门的命令标识符
            QString commandId = (deviceModel == "TM24ND-P-S") ? "Cal_WriteNum_TM24ND" : "Cal_WriteNum";
            result = sendCommand_Cal(sendData, commandId);
            if (result.first)
            {
                handleLogMessage("写入设备序列号成功！");
                // QMessageBox::information(calibrationDialog, "成功", "写入序列号成功！");

                // 新增需求：提示用户重启设备并等待确认
                QMessageBox restartPrompt(calibrationDialog);
                restartPrompt.setWindowTitle("设备重启");
                restartPrompt.setText("序列号写入成功！请重启设备后点击确定。");
                restartPrompt.setStandardButtons(QMessageBox::Ok);
                restartPrompt.setDefaultButton(QMessageBox::Ok);
                restartPrompt.exec(); // 模态显示，等待用户点击"确定"

                // 根据通信类型重新建立连接
                if (ui->communicationType_Cal->currentIndex() == 0)
                { // 串口模式
                    QString portName = ui->portNum_Cal->currentText();
                    int baudRate = ui->baudNum_Cal->currentText().toInt();

                    // 关闭当前串口
                    if (m_calDeviceSerialHandler->isPortOpen())
                    {
                        m_calDeviceSerialHandler->closePort();
                    }

                    // 重新打开串口
                    bool reopenSuccess = m_calDeviceSerialHandler->openPort(portName, baudRate);
                    if (reopenSuccess)
                    {
                        handleLogMessage("串口重新连接成功！");
                    }
                    else
                    {
                        QMessageBox::warning(this, "警告", "串口重新连接失败，请检查设备状态！");
                    }
                }
                else if (ui->communicationType_Cal->currentIndex() == 1)
                { // 网络模式
                    QString ipAddress = ui->ipAddress_Cal->text();
                    int port = ui->ipPort_Cal->text().toInt();

                    // 断开当前网络连接
                    if (m_calDeviceNetworkHandler->isConnected())
                    {
                        m_calDeviceNetworkHandler->disconnect();
                    }

                    // 重新连接网络
                    QPair<bool, QString> result = m_calDeviceNetworkHandler->connectToHost(ipAddress, port);
                    if (result.first)
                    {
                        handleLogMessage("网络重新连接成功！");
                    }
                    else
                    {
                        QMessageBox::warning(this, "警告", "网络重新连接失败，请检查设备状态！");
                    }
                }
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "写入序列号失败：" + result.second;
                handleLogMessage(errorMsg);
                QMessageBox::critical(calibrationDialog, "错误", errorMsg);
            }
        }
    }
    else if (featureCode == "writeBoardCardNumber")
    {
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "写入板卡编号"))
        {
            return;
        }

        handleLogMessage("开始写入板卡编号...");
        // 1618A-L和1618A-N使用不同的板卡编号地址
        QString boardCardAddr = (deviceModel == "1618A-L") ? "00 04 00 05 " : "03 4C 00 05 ";
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "0E" + boardCardAddr + sum + " " + asciiData.toHex(' '));
        result = sendCommand_Cal(sendData, "Cal_WriteBoardCardNumber");
        if (result.first)
        {
            handleLogMessage("写入板卡编号成功！");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "写入板卡编号失败：" + result.second;
            handleLogMessage(errorMsg);
            QMessageBox::critical(calibrationDialog, "错误", errorMsg);
        }
    }
    else if (featureCode == "writeBaudRate")
    {
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "写入波特率"))
        {
            return;
        }

        handleLogMessage("开始写入波特率...");
        // 此处获取设备型号值用来区分ZCLOG 334设备
        if (deviceModel == "ZCLOG 334NTC")
        {
            QString commandString = "FE FE FE FE 01 3B 08 " + asciiData.toHex(' ') + " 03";
            QString cleanCommand = commandString.simplified().remove(' ');
            QByteArray sendData = QByteArray::fromHex(cleanCommand.toLatin1());
            sendCommand_Cal(sendData, "Cal_WriteNum");
        }
        else
        {
            QString baudRateStr = baudRateMap2.value(paramVal);
            // 对TM24ND-P-S使用大端模式，其他设备使用小端模式
            if (deviceModel == "TM24ND-P-S")
            {
                // 解析小端格式的波特率值并转换为大端格式
                QStringList parts = baudRateStr.split(' ');
                if (parts.size() == 2)
                {
                    QString highByte = parts[1];            // 原来的高字节
                    QString lowByte = parts[0];             // 原来的低字节
                    baudRateStr = highByte + " " + lowByte; // 大端格式：高字节在前
                }
            }
            // TM24ND-P-S使用不同的波特率地址
            QString baudRateAddr = (deviceModel == "TM24ND-P-S") ? "00 01" : "00 02";
            QByteArray sendData = createModbusCommand(m_globalHeadStr + "06" + baudRateAddr + " " + baudRateStr);

            // 为TM24ND-P-S使用专门的命令标识符
            QString commandId = (deviceModel == "TM24ND-P-S") ? "Cal_WriteBaudRate_TM24ND" : "Cal_WriteBaudRate";
            result = sendCommand_Cal(sendData, commandId);
            if (result.first)
            {
                handleLogMessage("写入波特率成功：" + result.second);
                QMessageBox::information(calibrationDialog, "成功", "写入波特率：" + result.second + "成功！");
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "写入波特率失败：" + result.second;
                handleLogMessage(errorMsg);
                QMessageBox::critical(calibrationDialog, "错误", errorMsg);
            }
        }
    }
    else if (featureCode == "writeInit")
    {
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "执行设备初始化"))
        {
            return;
        }

        handleLogMessage("开始设备初始化...");
        if (deviceModel == "ZCLOG 334NTC")
        {
            QByteArray sendData = QByteArray::fromHex("FEFEFEFE01A00003"); // 恢复出厂设置参数
            // sendCommand_Cal(sendData, "isinit_ZCLOG334NTC");
        }
        else
        {
            QString m_resetAddr;
            m_resetAddr = getResetAddr(deviceModel);

            // 对TM24ND-P-S使用大端模式的00 00值
            QString resetValue = " 00 00"; // 00 00在大端和小端下都是一样的
            QByteArray sendData = createModbusCommand(m_globalHeadStr + "06" + m_resetAddr + resetValue);
            // 为TM24ND-P-S使用专门的命令标识符（如果需要特殊处理）
            QString commandId = (deviceModel == "TM24ND-P-S") ? "Cal_WriteInit_TM24ND" : "Cal_WriteInit";
            result = sendCommand_Cal(sendData, commandId);
            ;
            if (result.first)
            {
                handleLogMessage("设备初始化成功！");
                QMessageBox::information(calibrationDialog, "成功", "设备初始化成功！");
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "设备初始化失败：" + result.second;
                handleLogMessage(errorMsg);
                QMessageBox::critical(calibrationDialog, "错误", errorMsg);
            }
        }
    }
    else if (featureCode == "writeChannelRest")
    { // 废弃
        handleLogMessage("开始通道复位...");
        if (deviceModel == "ZCLOG 334NTC")
        {
            QByteArray sendData = QByteArray::fromHex("FEFEFEFE013D0003"); // 4通道复位 指令一致
            sendCommand_Cal(sendData, "isReset_ZCLOG334NTC");

            QTimer::singleShot(250, this, [this]()
                               {
                handleLogMessage("复位成功");
                QMessageBox::information(this, "提示", "复位成功"); });
        }
        else
        {
            QString m_resetAddr;
            m_resetAddr = getResetAddr(deviceModel);

            QString m_resetWriteVal = paramVal;
            // 对TM24ND-P-S的复位值进行大端处理
            if (deviceModel == "TM24ND-P-S")
            {
                // TM24ND-P-S需要大端格式，例如：01 00 -> 00 01
                QStringList parts = m_resetWriteVal.trimmed().split(' ');
                if (parts.size() == 2)
                {
                    QString highByte = parts[1];                      // 原来的高字节
                    QString lowByte = parts[0];                       // 原来的低字节
                    m_resetWriteVal = " " + highByte + " " + lowByte; // 大端格式：高字节在前
                }
            }
            QByteArray sendData = createModbusCommand(m_globalHeadStr + "06" + m_resetAddr + m_resetWriteVal);
            // 为TM24ND-P-S使用专门的命令标识符（如果需要特殊处理）
            QString commandId = (deviceModel == "TM24ND-P-S") ? "Cal_WriteChannelRest_TM24ND" : "Cal_WriteChannelRest";
            result = sendCommand_Cal(sendData, commandId);
            ;
            if (result.first)
            {
                handleLogMessage("当前通道复位成功！");
                QMessageBox::information(calibrationDialog, "成功", "当前通道复位成功！");
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "当前通道复位失败：" + result.second;
                handleLogMessage(errorMsg);
                QMessageBox::critical(calibrationDialog, "错误", errorMsg);
            }
        }
    }
    else if (featureCode == "writeFilterNum")
    {
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "写入滤波次数"))
        {
            return;
        }

        handleLogMessage("开始写入滤波次数...");
        if (deviceModel == "ZCLOG 334NTC")
        {
        }
        else
        {
            int decimal = paramVal.toInt();

            // 根据设备类型选择字节序
            QString valueStr;
            if (deviceModel == "TM24ND-P-S")
            {
                // TM24ND-P-S使用大端模式：高字节在前
                valueStr = formatValueForDevice(deviceModel, static_cast<uint16_t>(decimal));
            }
            else
            {
                // 其他设备使用小端模式
                QByteArray byteArray;
                byteArray.append(static_cast<char>(decimal & 0xFF));
                byteArray.append(static_cast<char>((decimal >> 8) & 0xFF));
                valueStr = byteArray.toHex(' ');
            }

            // 根据设备型号确定滤波次数地址
            QString filterAddr;
            if (deviceModel == "1611A-HT(PT100)" || deviceModel == "1611A-HT(PT1000)")
            {
                filterAddr = "00 03";
            }
            else if (deviceModel == "TM24ND-P-S")
            {
                filterAddr = "00 09";
            }
            else
            {
                filterAddr = "00 0A";
            }
            QByteArray sendData = createModbusCommand(m_globalHeadStr + "06" + filterAddr + " " + valueStr);

            // 为TM24ND-P-S使用专门的命令标识符
            QString commandId = (deviceModel == "TM24ND-P-S") ? "Cal_WriteFilterNum_TM24ND" : "Cal_WriteFilterNum";
            result = sendCommand_Cal(sendData, commandId);
            if (result.first)
            {
                handleLogMessage("写入滤波次数成功！");
                QMessageBox::information(calibrationDialog, "成功", "写入滤波次数成功！");
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "写入滤波次数失败：" + result.second;
                handleLogMessage(errorMsg);
                QMessageBox::critical(calibrationDialog, "错误", errorMsg);
            }
        }
    }
    else if (featureCode == "writeFilterNum1618A")
    {
        // 检查1618A-L是否需要透传模式
        QString deviceModel = calibrationDialog->getUi()->deviceModel_CalDialog->text();
        if (deviceModel == "1618A-L")
        {
            if (!checkTransparentModeRequirement(deviceModel, "写入滤波次数"))
            {
                return;
            }
        }

        handleLogMessage("开始写入1618A滤波次数...");
        int decimal = paramVal.toInt();

        // 1618A滤波次数统一使用小端模式
        QByteArray byteArray;
        byteArray.append(static_cast<char>(decimal & 0xFF));
        byteArray.append(static_cast<char>((decimal >> 8) & 0xFF));
        QString valueStr = byteArray.toHex(' ');

        // 1618A-L和1618A-N使用不同的滤波次数地址
        QString filterAddr = (deviceModel == "1618A-L") ? "00 0D " : "03 55 ";
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "06" + filterAddr + valueStr);

        result = sendCommand_Cal(sendData, "Cal_WriteFilterNum1618A");
        if (result.first)
        {
            handleLogMessage("写入1618A滤波次数成功！");
            QMessageBox::information(calibrationDialog, "成功", "写入1618A滤波次数成功！");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "写入1618A滤波次数失败：" + result.second;
            handleLogMessage(errorMsg);
            QMessageBox::critical(calibrationDialog, "错误", errorMsg);
        }
    }
}

void MainWindow::handleCalResults(const QPair<bool, QString> &result, const QString &errorMsg, const QString &successMsg)
{
    if (result.first)
    {
        emit signal_readCommand_CalDialog_Result(result.second, successMsg);
    }
    else
    {
        QMessageBox::critical(calibrationDialog, "错误", result.second == "timeout" ? "操作超时！" : errorMsg + result.second);
    }
}

void MainWindow::onHistoryDelBtnClicked()
{
    QListWidgetItem *selectedItem = calibrationDialog->getUi()->historyListWidget->currentItem();
    if (!selectedItem)
    {
        QMessageBox::warning(calibrationDialog, "警告", "请先选择一个项目");
        return;
    }

    QString projectName = selectedItem->text();
    int ret = QMessageBox::question(calibrationDialog, "确认删除",
                                    QString("确定要删除标定记录 '%1' 吗？").arg(projectName),
                                    QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes)
    {
        bool success = dbHandler->deleteRecord("calibration_records",
                                               QString("project_name = '%1'").arg(projectName));
        if (success)
        {
            delete selectedItem;      // 从列表中移除
            loadCalibrationHistory(); // 刷新历史列表
            QMessageBox::information(calibrationDialog, "成功", "标定记录已删除");
        }
        else
        {
            QMessageBox::warning(calibrationDialog, "错误", "删除标定记录失败");
        }
    }
}

void MainWindow::enterTransparentModeClicked()
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    handleLogMessage("开始进入透传模式...");

    QString msg = "start;cal\r\n";
    QByteArray sendData = msg.toLatin1();

    QPair<bool, QString> result = sendCommand_Cal(sendData, "Cal_EnterTransparentMode");
    if (result.first)
    {
        isWakeUp = true;                                                                      // 设置透传状态为真
        currentTransparentDevice = calibrationDialog->getUi()->deviceModel_CalDialog->text(); // 记录当前透传设备
        handleLogMessage("进入透传模式成功: " + result.second);
        QMessageBox::information(calibrationDialog, "透传模式", result.second);
    }
    else
    {
        QString errorMsg = result.second == "timeout" ? "操作超时！" : "进入透传模式失败：" + result.second;
        handleLogMessage(errorMsg);
        QMessageBox::critical(calibrationDialog, "错误", errorMsg);
    }
}

void MainWindow::quitTransparentModeClicked()
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    handleLogMessage("开始退出透传模式...");

    QString msg = "end;cal\r\n";
    QByteArray sendData = msg.toLatin1();

    QPair<bool, QString> result = sendCommand_Cal(sendData, "Cal_QuitTransparentMode");
    if (result.first)
    {
        isWakeUp = false;              // 设置透传状态为假
        currentTransparentDevice = ""; // 清空当前透传设备
        handleLogMessage("退出透传模式成功: " + result.second);
        QMessageBox::information(calibrationDialog, "透传模式", result.second);
    }
    else
    {
        QString errorMsg = result.second == "timeout" ? "操作超时！" : "退出透传模式失败：" + result.second;
        handleLogMessage(errorMsg);
        QMessageBox::critical(calibrationDialog, "错误", errorMsg);
    }
}

void MainWindow::enterTransparentModeClicked_Adj()
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    handleLogMessage_Adj("开始进入透传模式...");

    QString msg = "start;cal\r\n";
    QByteArray sendData = msg.toLatin1();

    QPair<bool, QString> result = sendCommand_Cal(sendData, "Cal_EnterTransparentMode");
    if (result.first)
    {
        isWakeUp = true;                                                                 // 设置透传状态为真
        currentTransparentDevice = verificationDialog->getUi()->deviceModel_Adj->text(); // 记录当前透传设备
        handleLogMessage_Adj("进入透传模式成功: " + result.second);
        QMessageBox::information(verificationDialog, "透传模式", result.second);
    }
    else
    {
        QString errorMsg = result.second == "timeout" ? "操作超时！" : "进入透传模式失败：" + result.second;
        handleLogMessage_Adj(errorMsg);
        QMessageBox::critical(verificationDialog, "错误", errorMsg);
    }
}

void MainWindow::quitTransparentModeClicked_Adj()
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    handleLogMessage_Adj("开始退出透传模式...");

    QString msg = "end;cal\r\n";
    QByteArray sendData = msg.toLatin1();

    QPair<bool, QString> result = sendCommand_Cal(sendData, "Cal_QuitTransparentMode");
    if (result.first)
    {
        isWakeUp = false;              // 设置透传状态为假
        currentTransparentDevice = ""; // 清空当前透传设备
        handleLogMessage_Adj("退出透传模式成功: " + result.second);
        QMessageBox::information(verificationDialog, "透传模式", result.second);
    }
    else
    {
        QString errorMsg = result.second == "timeout" ? "操作超时！" : "退出透传模式失败：" + result.second;
        handleLogMessage_Adj(errorMsg);
        QMessageBox::critical(verificationDialog, "错误", errorMsg);
    }
}

void MainWindow::resetTransparentModeState()
{
    if (isWakeUp)
    {
        // 根据当前活动的对话框选择相应的日志记录方式
        if (calibrationDialog && calibrationDialog->isVisible())
        {
            handleLogMessage("检测到设备型号变更或连接状态变化，自动重置透传模式状态");
        }
        else if (verificationDialog && verificationDialog->isVisible())
        {
            handleLogMessage_Adj("检测到设备型号变更或连接状态变化，自动重置透传模式状态");
        }
        else
        {
            // 主界面设备变更时，两个日志都输出，确保用户能看到
            handleLogMessage("检测到设备型号变更或连接状态变化，自动重置透传模式状态");
            handleLogMessage_Adj("检测到设备型号变更或连接状态变化，自动重置透传模式状态");
        }
    }
    isWakeUp = false;
    currentTransparentDevice = "";
}

bool MainWindow::checkTransparentModeRequirement(const QString &deviceModel, const QString &operationName)
{
    // 检查是否为透传模式设备
    if (!transparentModeDevices.contains(deviceModel))
    {
        return true; // 非透传模式设备，直接允许操作
    }

    if (!isWakeUp)
    {
        // 透传模式设备未唤醒：不允许操作
        handleLogMessage(QString("透传模式设备未唤醒，不能%1").arg(operationName));
        // 根据当前活动的对话框选择显示位置
        QWidget *parentWidget = this;
        if (calibrationDialog && calibrationDialog->isVisible())
        {
            parentWidget = calibrationDialog;
        }
        else if (verificationDialog && verificationDialog->isVisible())
        {
            parentWidget = verificationDialog;
        }
        QMessageBox::warning(parentWidget, "提示", QString("请先进入透传模式再%1！").arg(operationName));
        return false;
    }

    if (currentTransparentDevice != deviceModel)
    {
        handleLogMessage("检测到设备型号不一致，自动重置透传模式状态");
        resetTransparentModeState();
        // 根据当前活动的对话框选择显示位置
        QWidget *parentWidget = this;
        if (calibrationDialog && calibrationDialog->isVisible())
        {
            parentWidget = calibrationDialog;
        }
        else if (verificationDialog && verificationDialog->isVisible())
        {
            parentWidget = verificationDialog;
        }
        QMessageBox::warning(parentWidget, "提示", QString("请先进入透传模式再%1！").arg(operationName));
        return false;
    }

    return true; // 透传模式设备已正确唤醒，允许操作
}

bool MainWindow::checkTransparentModeBeforeOperation(const QString &operationName)
{
    if (isWakeUp && !currentTransparentDevice.isEmpty())
    {
        // 根据当前活动的对话框选择显示位置
        QWidget *parentWidget = this;
        if (calibrationDialog && calibrationDialog->isVisible())
        {
            parentWidget = calibrationDialog;
        }
        else if (verificationDialog && verificationDialog->isVisible())
        {
            parentWidget = verificationDialog;
        }

        QMessageBox::StandardButton reply = QMessageBox::question(
            parentWidget,
            "透传模式提醒",
            QString("设备 %1 当前处于透传模式，建议先退出透传模式再%2。\n\n是否强制继续操作？").arg(currentTransparentDevice).arg(operationName),
            QMessageBox::Yes | QMessageBox::No,
            QMessageBox::No);

        if (reply == QMessageBox::No)
        {
            return false; // 用户选择不继续操作
        }
        else
        {
            // 用户选择强制继续，重置透传状态
            handleLogMessage(QString("用户强制%1，自动重置透传模式状态").arg(operationName));
            resetTransparentModeState();
        }
    }
    return true; // 允许操作
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (isWakeUp && !currentTransparentDevice.isEmpty())
    {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this,
            "透传模式提醒",
            QString("设备 %1 当前处于透传模式，建议先退出透传模式再关闭程序。\n\n是否强制关闭程序？").arg(currentTransparentDevice),
            QMessageBox::Yes | QMessageBox::No,
            QMessageBox::No);

        if (reply == QMessageBox::No)
        {
            event->ignore(); // 取消关闭
            return;
        }
    }

    // 执行正常的关闭流程
    QMainWindow::closeEvent(event);
}

QString MainWindow::getUnitFromModel(const QString &deviceModel)
{
    if (deviceModel == "TM22XND")
    {
        return "V";
    }
    else
    {
        return "Ω";
    }
}

void MainWindow::onHistoryItemDoubleClicked(QListWidgetItem *item)
{
    QString projectName = item->text();
    auto result = dbHandler->queryRecords("calibration_records", QString("project_name = '%1'").arg(projectName));

    if (result.first && !result.second.isEmpty())
    {
        QSqlRecord record = result.second.first();
        QString deviceModel = record.value("device_model").toString();
        double referenceValue = record.value("reference_value").toDouble();
        QString channelValuesJson = record.value("channel_values").toString();
        QString deviationsJson = record.value("deviations").toString();

        // 解析 JSON 数据
        QJsonDocument channelDoc = QJsonDocument::fromJson(channelValuesJson.toUtf8());
        QJsonDocument deviationsDoc = QJsonDocument::fromJson(deviationsJson.toUtf8());
        QJsonArray channelValuesArray = channelDoc.array();
        QJsonArray deviationsArray = deviationsDoc.array();

        // 以 channel_values 的长度确定通道数量
        int numChannels = channelValuesArray.size();
        if (numChannels != deviationsArray.size())
        {
            QMessageBox::warning(this, "错误", "通道数据和偏差数据长度不一致");
            return;
        }

        // 获取单位
        QString unit = getUnitFromModel(deviceModel);

        // 初始化表格
        calibrationDialog->getUi()->readDataTable->clear();
        calibrationDialog->getUi()->readDataTable->setColumnCount(2 + numChannels); // 描述列 + 参考列 + 通道列
        calibrationDialog->getUi()->readDataTable->setRowCount(3);                  // 标题行 + 测量值行 + 偏差行

        // 设置表格属性
        calibrationDialog->getUi()->readDataTable->verticalHeader()->setVisible(false);
        calibrationDialog->getUi()->readDataTable->horizontalHeader()->setVisible(false);
        calibrationDialog->getUi()->readDataTable->horizontalHeader()->setMinimumSectionSize(88);
        calibrationDialog->getUi()->readDataTable->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        calibrationDialog->getUi()->readDataTable->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

        // 设置第一行（标题行）
        QTableWidgetItem *emptyItem = new QTableWidgetItem("");
        emptyItem->setBackground(QBrush(QColor(240, 240, 240)));
        calibrationDialog->getUi()->readDataTable->setItem(0, 0, emptyItem);

        QTableWidgetItem *refHeaderItem = new QTableWidgetItem("参考（" + unit + "）");
        refHeaderItem->setBackground(QBrush(QColor(240, 240, 240)));
        refHeaderItem->setTextAlignment(Qt::AlignCenter);
        calibrationDialog->getUi()->readDataTable->setItem(0, 1, refHeaderItem);

        for (int ch = 0; ch < numChannels; ++ch)
        {
            QTableWidgetItem *chHeaderItem = new QTableWidgetItem(QString("CH%1（%2）").arg(ch + 1).arg(unit));
            chHeaderItem->setBackground(QBrush(QColor(240, 240, 240)));
            chHeaderItem->setTextAlignment(Qt::AlignCenter);
            calibrationDialog->getUi()->readDataTable->setItem(0, ch + 2, chHeaderItem);
        }

        // 设置第一列（测量值和偏差标题）
        QTableWidgetItem *measureHeaderItem = new QTableWidgetItem("测量值");
        measureHeaderItem->setBackground(QBrush(QColor(240, 240, 240)));
        measureHeaderItem->setTextAlignment(Qt::AlignCenter);
        calibrationDialog->getUi()->readDataTable->setItem(1, 0, measureHeaderItem);

        QTableWidgetItem *deviationHeaderItem = new QTableWidgetItem("偏差");
        deviationHeaderItem->setBackground(QBrush(QColor(240, 240, 240)));
        deviationHeaderItem->setTextAlignment(Qt::AlignCenter);
        calibrationDialog->getUi()->readDataTable->setItem(2, 0, deviationHeaderItem);

        // 填充参考值
        QTableWidgetItem *refValueItem = new QTableWidgetItem(QString::number(referenceValue, 'f', 6));
        refValueItem->setTextAlignment(Qt::AlignCenter);
        calibrationDialog->getUi()->readDataTable->setItem(1, 1, refValueItem);

        QTableWidgetItem *refDeviationItem = new QTableWidgetItem("0");
        refDeviationItem->setTextAlignment(Qt::AlignCenter);
        calibrationDialog->getUi()->readDataTable->setItem(2, 1, refDeviationItem);

        // 填充通道数据
        for (int ch = 0; ch < numChannels; ++ch)
        {
            if (ch < channelValuesArray.size())
            {
                double value = channelValuesArray[ch].toDouble();
                QTableWidgetItem *valueItem = new QTableWidgetItem(QString::number(value, 'f', 6));
                valueItem->setTextAlignment(Qt::AlignCenter);
                calibrationDialog->getUi()->readDataTable->setItem(1, ch + 2, valueItem);
            }
            else
            {
                QTableWidgetItem *valueItem = new QTableWidgetItem("");
                valueItem->setTextAlignment(Qt::AlignCenter);
                calibrationDialog->getUi()->readDataTable->setItem(1, ch + 2, valueItem);
            }

            if (ch < deviationsArray.size())
            {
                double deviation = deviationsArray[ch].toDouble();
                QTableWidgetItem *deviationItem = new QTableWidgetItem(QString::number(deviation, 'f', 6));
                deviationItem->setTextAlignment(Qt::AlignCenter);
                deviationItem->setForeground(Qt::green); // 设置前景色为绿色
                calibrationDialog->getUi()->readDataTable->setItem(2, ch + 2, deviationItem);
            }
            else
            {
                QTableWidgetItem *deviationItem = new QTableWidgetItem("");
                deviationItem->setTextAlignment(Qt::AlignCenter);
                calibrationDialog->getUi()->readDataTable->setItem(2, ch + 2, deviationItem);
            }
        }

        // 设置表格自适应
        for (int i = 0; i < calibrationDialog->getUi()->readDataTable->columnCount(); ++i)
        {
            calibrationDialog->getUi()->readDataTable->horizontalHeader()->setSectionResizeMode(i, QHeaderView::Stretch);
        }

        // 美化表格
        calibrationDialog->getUi()->readDataTable->setStyleSheet("QTableWidget { gridline-color: #CCCCCC; }");
        calibrationDialog->getUi()->readDataTable->setShowGrid(true);
    }
    else
    {
        QMessageBox::warning(calibrationDialog, "错误", "无法加载标定记录");
    }
}

void MainWindow::loadCalibrationHistory()
{
    if (dbHandler->isDatabaseConnected())
    {
        calibrationDialog->getUi()->historyListWidget->clear();
        auto result = dbHandler->queryRecords("calibration_records"); // 假设 dbHandler 是数据库操作类
        if (result.first)
        {
            for (const auto &record : result.second)
            {
                QString projectName = record.value("project_name").toString();
                calibrationDialog->getUi()->historyListWidget->addItem(projectName);
            }
        }
        else
        {
            QMessageBox::warning(calibrationDialog, "错误", "加载历史记录失败");
        }
    }
    else
    {
        // qDebug() << "数据库连接出错";
        handleLogMessage("数据库连接出错");
    }
}

// 替换原有的startCalibration函数为线程版本
void MainWindow::generateCalTable(CalDeviceConfig &device)
{
    // 清空表格
    calibrationDialog->getUi()->readDataTable->clear();

    // 隐藏行号和列号
    calibrationDialog->getUi()->readDataTable->verticalHeader()->setVisible(false);
    calibrationDialog->getUi()->readDataTable->horizontalHeader()->setVisible(false);

    // 设置最小列宽
    calibrationDialog->getUi()->readDataTable->horizontalHeader()->setMinimumSectionSize(88);

    // 设置滚动条策略
    calibrationDialog->getUi()->readDataTable->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    calibrationDialog->getUi()->readDataTable->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 设置表格行列数
    calibrationDialog->getUi()->readDataTable->setColumnCount(1 + device.num_channels + 1); // 描述列 + 参考列 + 通道列
    calibrationDialog->getUi()->readDataTable->setRowCount(3);                              // 标题行 + 测量值行 + 偏差行

    // 定义样式
    QColor headerRowBgColor(240, 240, 240); // 第一行背景色
    QColor headerColBgColor(240, 240, 240); // 第一列背景色

    // 设置第一行(标题行)
    QString unit = (device.name != "TM22XND") ? "（Ω）" : "V"; // 判断单位

    // 第一行第一列 - 空白
    QTableWidgetItem *emptyItem = new QTableWidgetItem("");
    emptyItem->setBackground(QBrush(QColor(240, 240, 240))); // 特殊角落颜色
    calibrationDialog->getUi()->readDataTable->setItem(0, 0, emptyItem);

    // 第一行其他列 - 参考和通道标题
    QTableWidgetItem *refHeaderItem = new QTableWidgetItem("参考" + unit);
    refHeaderItem->setBackground(headerRowBgColor);
    refHeaderItem->setTextAlignment(Qt::AlignCenter);
    // refHeaderItem->setFont(QFont("Arial", 10));
    calibrationDialog->getUi()->readDataTable->setItem(0, 1, refHeaderItem);

    for (int ch = 0; ch < device.num_channels; ++ch)
    {
        QTableWidgetItem *chHeaderItem = new QTableWidgetItem(QString("CH%1%2").arg(ch + 1).arg(unit));
        chHeaderItem->setBackground(headerRowBgColor);
        chHeaderItem->setTextAlignment(Qt::AlignCenter);
        // chHeaderItem->setFont(QFont("Arial", 10));
        calibrationDialog->getUi()->readDataTable->setItem(0, ch + 2, chHeaderItem);
    }

    // 设置第一列(测量值和偏差标题)
    QTableWidgetItem *measureHeaderItem = new QTableWidgetItem("测量值");
    measureHeaderItem->setBackground(headerColBgColor);
    measureHeaderItem->setTextAlignment(Qt::AlignCenter);
    // measureHeaderItem->setFont(QFont("Arial", 10));
    calibrationDialog->getUi()->readDataTable->setItem(1, 0, measureHeaderItem);

    QTableWidgetItem *deviationHeaderItem = new QTableWidgetItem("偏差");
    deviationHeaderItem->setBackground(headerColBgColor);
    deviationHeaderItem->setTextAlignment(Qt::AlignCenter);
    // deviationHeaderItem->setFont(QFont("Arial", 10));
    calibrationDialog->getUi()->readDataTable->setItem(2, 0, deviationHeaderItem);

    // 填充参考值
    QTableWidgetItem *refValueItem = new QTableWidgetItem(QString::number(device.ref_values[0], 'f', 6));
    refValueItem->setTextAlignment(Qt::AlignCenter);
    calibrationDialog->getUi()->readDataTable->setItem(1, 1, refValueItem);

    // 偏差参考值
    QTableWidgetItem *refDeviationItem = new QTableWidgetItem("0"); // 默认 0
    refDeviationItem->setTextAlignment(Qt::AlignCenter);
    // refDeviationItem->setForeground(Qt::green);
    calibrationDialog->getUi()->readDataTable->setItem(2, 1, refDeviationItem);

    // 设置通道测量值和偏差格子(初始为空)
    for (int ch = 0; ch < device.num_channels; ++ch)
    {
        QTableWidgetItem *valueItem = new QTableWidgetItem("");
        valueItem->setTextAlignment(Qt::AlignCenter);
        calibrationDialog->getUi()->readDataTable->setItem(1, ch + 2, valueItem);

        QTableWidgetItem *deviationValueItem = new QTableWidgetItem("");
        deviationValueItem->setTextAlignment(Qt::AlignCenter);
        calibrationDialog->getUi()->readDataTable->setItem(2, ch + 2, deviationValueItem);
    }

    // 设置表格自适应 (必须在设置项目后调用)
    for (int i = 0; i < calibrationDialog->getUi()->readDataTable->columnCount(); ++i)
    {
        calibrationDialog->getUi()->readDataTable->horizontalHeader()->setSectionResizeMode(i, QHeaderView::Stretch);
    }

    // 整体美化
    calibrationDialog->getUi()->readDataTable->setStyleSheet("QTableWidget { gridline-color: #CCCCCC; }");
    calibrationDialog->getUi()->readDataTable->setShowGrid(true);
}

void MainWindow::startCalibrationInThread(CalDeviceConfig &device)
{
    // 显示校准对话框
    if (!calibrationDialog->isVisible())
    {
        // calibrationDialog->show();

        // 使用 exec() 以模态方式显示对话框
        calibrationDialog->setWindowModality(Qt::ApplicationModal);
        calibrationDialog->exec();
    }

    // calibrationDialog->getUi()->textEdit->clear();
    calibrationDialog->getUi()->progressBar->setValue(0);

    // 生成校准表格
    generateCalTable(device);

    // 16通道及以内的通道设备 无需分批 优先插入尾部接口
    const int thresholdForBatchCalibration = 16;
    if (device.num_channels > thresholdForBatchCalibration)
    {
        // Use batch calibration for devices with many channels
        startBatchCalibrationInThread(device);
    }
    else
    {
        // Use regular calibration for devices with fewer channels
        calibrationWorker->setDeviceConfig(device);

        if (calibrationDialog->getUi()->startCal_CalDialog)
        {
            calibrationDialog->getUi()->startCal_CalDialog->setEnabled(false);
        }

        // Start regular calibration
        emit startCalibration1();
    }
}

void MainWindow::handleCalibrationStarted()
{
    // 校准开始时的处理
    handleLogMessage("标定进程已启动");

    if (calibrationDialog->getUi()->startCal_CalDialog)
    {
        calibrationDialog->getUi()->startCal_CalDialog->setEnabled(false);
    }

    if (calibrationDialog->getUi()->abortButton)
    {
        calibrationDialog->getUi()->abortButton->setEnabled(true);
    }

    if (calibrationDialog->getUi()->restartCal_CalDialog)
    {
        calibrationDialog->getUi()->restartCal_CalDialog->setEnabled(false);
    }

    if (calibrationDialog->getUi()->endCal_CalDialog)
    {
        calibrationDialog->getUi()->endCal_CalDialog->setEnabled(false);
    }
}

void MainWindow::handleCalibrationFinished(bool success)
{
    // 校准完成时的处理
    if (calibrationDialog->getUi()->startCal_CalDialog)
    {
        calibrationDialog->getUi()->startCal_CalDialog->setEnabled(true);
    }

    if (calibrationDialog->getUi()->abortButton)
    {
        calibrationDialog->getUi()->abortButton->setEnabled(false);
    }

    if (calibrationDialog->getUi()->restartCal_CalDialog)
    {
        calibrationDialog->getUi()->restartCal_CalDialog->setEnabled(true);
    }

    if (calibrationDialog->getUi()->endCal_CalDialog)
    {
        calibrationDialog->getUi()->endCal_CalDialog->setEnabled(true);
    }

    if (success)
    {
        QMessageBox::information(calibrationDialog, "完成", "标定过程已成功完成！");
    }
    else
    {
        calibrationDialog->getUi()->progressBar->setValue(0); // 重置进度条
        QMessageBox::warning(calibrationDialog, "警告", "标定过程未完成或出现错误！");
    }
}

void MainWindow::handleCalibrationProgress(int channel, int progress)
{
    // 更新进度条
    calibrationDialog->getUi()->progressBar->setValue(progress);
}

void MainWindow::handleLogMessage(const QString &message)
{
    // 获取当前日期时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString timestamp = currentDateTime.toString("yyyy/MM/dd hh:mm:ss");
    QString dateString = currentDateTime.toString("yyyyMMdd");

    // 将消息添加到日志文本框
    calibrationDialog->getUi()->textEdit->append(QString("%1\n%2\n").arg(timestamp).arg(message));

    // 移动光标到文本框的末尾
    QTextCursor cursor = calibrationDialog->getUi()->textEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    calibrationDialog->getUi()->textEdit->setTextCursor(cursor);

    // 构建日志目录路径
    QString appDir = QCoreApplication::applicationDirPath();
    QString logDirPath = QDir(appDir).filePath("Auto_TCal_Log");

    // 检查并创建日志目录
    QDir logDir(logDirPath);
    if (!logDir.exists())
    {
        if (!logDir.mkpath("."))
        {
            qDebug() << "无法创建日志目录:" << logDirPath;
            return;
        }
    }

    // 构建日志文件名
    QString logFileName = QString("Auto_TCal_Log_%1.log").arg(dateString);

    // 构建日志文件路径
    QString logFilePath = logDir.filePath(logFileName);

    // 打开日志文件进行追加写入
    QFile logFile(logFilePath);
    if (logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
    {
        QTextStream stream(&logFile);
        stream << timestamp << "\n"
               << message << "\n\n";
        logFile.close();
    }
    else
    {
        // 如果打开文件失败，可以在UI上显示错误信息
        qDebug() << "无法打开日志文件进行写入:" << logFile.errorString();
    }
}

void MainWindow::handleUpdateChannelData(int channel, double resistance, double deviation, bool passed)
{
    // 更新表格中的通道数据
    calibrationDialog->getUi()->readDataTable->item(1, channel + 2)->setText(QString::number(resistance, 'f', 6)); // 测量值
    calibrationDialog->getUi()->readDataTable->item(2, channel + 2)->setText(QString::number(deviation, 'f', 6));  // 偏差

    // 根据偏差设置颜色
    if (passed)
    {
        calibrationDialog->getUi()->readDataTable->item(2, channel + 2)->setForeground(Qt::green);
    }
    else
    {
        calibrationDialog->getUi()->readDataTable->item(2, channel + 2)->setForeground(Qt::red);
    }
}

void MainWindow::handleSaveResultsRequested(const CalDeviceConfig &config, const QVector<QPair<double, double>> &results)
{
    QString modelName;
    QString serialNumber;
    if (ui->deviceModel_Cal->currentText() == "1618A-N" || ui->deviceModel_Cal->currentText() == "1618A-L")
    {
        modelName = calibrationDialog->getUi()->boardCardModel_Combo_CalDialog->currentText();
        serialNumber = calibrationDialog->getUi()->boardCardNumberEdit_CalDialog->text();
    }
    else
    {
        modelName = config.name;
        serialNumber = calibrationDialog->getUi()->serialNumEdit_Cal->text();
    }

    // 生成项目名称：设备型号+序列号+日期时间
    QString projectName = QString("%1_%2_%3")
                              .arg(modelName)
                              .arg(serialNumber)
                              .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmss"));

    // 准备通道数据和偏差数据的 JSON 字符串
    QJsonArray channelValuesArray;
    QJsonArray deviationsArray;
    for (const auto &result : results)
    {
        channelValuesArray.append(result.first); // 最后一轮阻值
        deviationsArray.append(result.second);   // 最后一轮偏差
    }
    QJsonDocument channelDoc(channelValuesArray);
    QJsonDocument deviationsDoc(deviationsArray);
    QString channelValuesJson = channelDoc.toJson(QJsonDocument::Compact);
    QString deviationsJson = deviationsDoc.toJson(QJsonDocument::Compact);

    // 准备插入数据
    RecordData data; // 假设 RecordData 是 DbHandler 接受的数据类型，例如 QMap<QString, QVariant>
    data["project_name"] = projectName;
    data["device_model"] = modelName;
    data["serial_number"] = serialNumber;
    data["calibration_date"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    data["reference_value"] = config.ref_values[0]; // 假设参考值取第一个通道的
    data["channel_values"] = channelValuesJson;
    data["deviations"] = deviationsJson;
    data["status"] = "passed";

    // 618A-10086-20250308; 618A;10086;20250308;999.999;[100,200,300];[0.1,0.2,0.3];passed;
    // 通过 dbHandler 插入记录
    bool insertSuccess = dbHandler->insertRecord("calibration_records", data);
    if (insertSuccess)
    {
        handleLogMessage("标定记录已保存: " + projectName);
        loadCalibrationHistory(); // 刷新项目记录列表
    }
    else
    {
        handleLogMessage("保存标定记录失败");
    }
}

void MainWindow::abortCalibration()
{
    int ret = QMessageBox::question(calibrationDialog, "确认", "确定要中止标定过程吗？",
                                    QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes)
    {
        if (isBatchCalibrationInProgress)
        {
            batchCalibrationManager->abortCalibration();
        }
        else
        {
            calibrationWorker->abortCalibration();
        }
    }
}

void MainWindow::on_startCalibration_clicked()
{
    // 根据选中的校准设备型号 动态初始化标定页面
    QStringList refers;
    if (ui->refer1->isChecked())
    {
        refers.append(ui->refer1->text());
    }
    if (ui->refer2->isChecked())
    {
        refers.append(ui->refer2->text());
    }
    if (ui->refer3->isChecked())
    {
        refers.append(ui->refer3->text());
    }
    if (ui->refer4->isChecked())
    {
        refers.append(ui->refer4->text());
    }
    if (ui->refer5->isChecked())
    {
        refers.append(ui->refer5->text());
    }

    calibrationDialog->initCalibrationDialog(ui->deviceModel_Cal->currentText(), ui->chNums_Cal->currentText().toInt(), refers);

    // 使用 exec() 以模态方式显示对话框
    calibrationDialog->setWindowModality(Qt::ApplicationModal);
    calibrationDialog->exec();
}

void MainWindow::on_recalibrateButton_CalDialog_clicked()
{ // 复校一定要在一次开始校准之后进行 1、正常结束2、手动停止校准
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    // 判断参考是否为空等
    if (calibrationDialog->getUi()->refers_CalDialog->count() == 0)
    {
        QMessageBox::warning(this, "警告", "当前未勾选参考电阻，请至少选择一个参考电阻。");
        return;
    }

    if (calibrationDialog->getUi()->deviceModel_CalDialog->text() == "1618A-N" || calibrationDialog->getUi()->deviceModel_CalDialog->text() == "1618A-L")
    {
        if (calibrationDialog->getUi()->boardCardNumberEdit_CalDialog->text().isEmpty())
        {
            QMessageBox::warning(this, "警告", "请先执行系统参数校准。");
            return;
        }
    }
    else
    {
        if (calibrationDialog->getUi()->serialNumEdit_Cal->text().isEmpty())
        {
            QMessageBox::warning(this, "警告", "请先执行系统参数校准。");
            return;
        }
    }

    QTableWidget *table = calibrationDialog->getUi()->readDataTable;
    QList<QTableWidgetItem *> selectedItems = table->selectedItems();

    // 检查是否选中了单元格
    if (selectedItems.isEmpty())
    {
        QMessageBox::warning(calibrationDialog, "提示", "请先选中一个单元格");
        return;
    }

    // 获取选中的单元格（假设每次只选中一个）
    QTableWidgetItem *selectedItem = selectedItems.first();
    int row = selectedItem->row();    // 选中单元格的行号
    int col = selectedItem->column(); // 选中单元格的列号

    // 标定无需获取参考阻值，每次只校准同一参考电阻阻值

    // 通道号直接由列号确定（假设第1列对应通道1，第2列对应通道2，以此类推）1-n
    int channel = col - 1;

    // qDebug() << "referenceValue: " << referenceValue;
    // qDebug() << "channel: " << channel;

    if (calibrationDialog->getUi()->startCal_CalDialog)
    {
        calibrationDialog->getUi()->startCal_CalDialog->setEnabled(false);
    }

    if (calibrationDialog->getUi()->abortButton)
    {
        calibrationDialog->getUi()->abortButton->setEnabled(true);
    }

    if (calibrationDialog->getUi()->restartCal_CalDialog)
    {
        calibrationDialog->getUi()->restartCal_CalDialog->setEnabled(false);
    }

    if (calibrationDialog->getUi()->endCal_CalDialog)
    {
        calibrationDialog->getUi()->endCal_CalDialog->setEnabled(false);
    }

    // 根据通道数决定不同的复校方式
    if (batchCalibrationManager && (ui->chNums_Cal->currentText().toInt() > 16))
    {
        isBatchCalibrationInProgress = true;
        batchCalibrationManager->recalibrateChannel(channel);
    }
    else
    {
        // 触发复校操作
        emit restartCalibration1(channel); // 设备通道、参考阻值借用已设定好的deviceconfig
    }
}

void MainWindow::on_endCal_CalDialog_clicked()
{
    // 确认用户是否要结束校准并保存结果
    QMessageBox::StandardButton reply = QMessageBox::question(calibrationDialog,
                                                              "确认保存", "是否结束标定并保存结果？",
                                                              QMessageBox::Yes | QMessageBox::No);

    if (reply != QMessageBox::Yes)
    {
        return;
    }

    // 获取表格中的数据
    QTableWidget *table = calibrationDialog->getUi()->readDataTable;
    if (table->rowCount() < 2)
    {
        return;
    }

    int numChannels = ui->chNums_Cal->currentText().toInt();

    // 创建结果向量，与自动标定相同的格式
    QVector<QPair<double, double>> finalResults;
    bool allChannelsPassed = true; // 跟踪是否所有通道都通过了

    // 从表格中提取数据
    for (int channel = 0; channel < numChannels; ++channel)
    {
        // 读取阻值（表格中的行1，列从2开始，对应通道0）
        QTableWidgetItem *resistanceItem = table->item(1, channel + 2);
        QTableWidgetItem *deviationItem = table->item(2, channel + 2);

        if (resistanceItem && deviationItem && !resistanceItem->text().isEmpty() && !deviationItem->text().isEmpty())
        {
            double resistance = resistanceItem->text().toDouble();
            double deviation = deviationItem->text().toDouble();
            finalResults.append(qMakePair(resistance, deviation));

            // 检查当前通道是否通过（根据偏差值颜色判断）
            if (deviationItem->foreground() == Qt::red)
            {
                allChannelsPassed = false;
            }
        }
        else
        {
            // 如果表格数据不完整，添加默认值
            finalResults.append(qMakePair(0.0, 0.0));
            allChannelsPassed = false; // 不完整的数据视为未通过
            handleLogMessage(QString("警告: 通道 %1 数据不完整").arg(channel + 1));
        }
    }

    // 检查是否所有通道都通过了，如果没有，则不保存
    if (!allChannelsPassed)
    {
        QMessageBox::warning(calibrationDialog, "无法保存", "只能保存所有通道都通过的标定记录。请继续校准未通过的通道。");
        return;
    }

    QString modelName;
    QString serialNumber;
    if (ui->deviceModel_Cal->currentText() == "1618A-N" || ui->deviceModel_Cal->currentText() == "1618A-L")
    {
        modelName = calibrationDialog->getUi()->boardCardModel_Combo_CalDialog->currentText();
        serialNumber = calibrationDialog->getUi()->boardCardNumberEdit_CalDialog->text();
    }
    else
    {
        modelName = ui->deviceModel_Cal->currentText();
        serialNumber = calibrationDialog->getUi()->serialNumEdit_Cal->text();
    }

    // 准备插入数据
    RecordData data; // 假设 RecordData 是 DbHandler 接受的数据类型，例如 QMap<QString, QVariant>
    QString projectName = QString("%1_%2_%3")
                              .arg(modelName)
                              .arg(serialNumber)
                              .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmss"));

    // 准备通道数据和偏差数据的 JSON 字符串
    QJsonArray channelValuesArray;
    QJsonArray deviationsArray;
    for (const auto &result : finalResults)
    {
        channelValuesArray.append(result.first); // 阻值
        deviationsArray.append(result.second);   // 偏差
    }
    QJsonDocument channelDoc(channelValuesArray);
    QJsonDocument deviationsDoc(deviationsArray);
    QString channelValuesJson = channelDoc.toJson(QJsonDocument::Compact);
    QString deviationsJson = deviationsDoc.toJson(QJsonDocument::Compact);

    data["project_name"] = projectName;
    data["device_model"] = modelName;
    data["serial_number"] = serialNumber;
    data["calibration_date"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    data["reference_value"] = calibrationDialog->getUi()->readDataTable->item(1, 1)->text().toDouble(); // 取表参考阻值
    data["channel_values"] = channelValuesJson;
    data["deviations"] = deviationsJson;
    data["status"] = "passed"; // 既然所有通道都通过了，状态就是passed

    // 通过 dbHandler 插入记录
    bool insertSuccess = dbHandler->insertRecord("calibration_records", data);
    if (insertSuccess)
    {
        handleLogMessage("标定记录已保存: " + projectName);
        loadCalibrationHistory(); // 刷新项目记录列表
    }
    else
    {
        handleLogMessage("保存标定记录失败");
    }

    handleLogMessage("手动校准完成，结果已保存");
}

//-------------分批标定
void MainWindow::startBatchCalibrationInThread(CalDeviceConfig &device)
{
    if (calibrationDialog->getUi()->startCal_CalDialog)
    {
        calibrationDialog->getUi()->startCal_CalDialog->setEnabled(false);
    }

    if (calibrationDialog->getUi()->abortButton)
    {
        calibrationDialog->getUi()->abortButton->setEnabled(true);
    }

    if (calibrationDialog->getUi()->restartCal_CalDialog)
    {
        calibrationDialog->getUi()->restartCal_CalDialog->setEnabled(false);
    }

    if (calibrationDialog->getUi()->endCal_CalDialog)
    {
        calibrationDialog->getUi()->endCal_CalDialog->setEnabled(false);
    }

    isBatchCalibrationInProgress = true;

    // Start batch calibration
    batchCalibrationManager->startBatchCalibration(device, calCommandHandler, deviceCommandHandler);
}

// Add handlers for batch calibration signals
void MainWindow::handleBatchCalibrationCompleted(bool success)
{
    isBatchCalibrationInProgress = false;

    if (calibrationDialog->getUi()->startCal_CalDialog)
    {
        calibrationDialog->getUi()->startCal_CalDialog->setEnabled(true);
    }

    if (calibrationDialog->getUi()->abortButton)
    {
        calibrationDialog->getUi()->abortButton->setEnabled(false);
    }

    if (calibrationDialog->getUi()->restartCal_CalDialog)
    {
        calibrationDialog->getUi()->restartCal_CalDialog->setEnabled(true);
    }

    if (calibrationDialog->getUi()->endCal_CalDialog)
    {
        calibrationDialog->getUi()->endCal_CalDialog->setEnabled(true);
    }

    if (success)
    {
        QMessageBox::information(calibrationDialog, "完成", "批次标定过程已成功完成！");
    }
    else
    {
        calibrationDialog->getUi()->progressBar->setValue(0); // 重置进度条
        QMessageBox::warning(calibrationDialog, "警告", "批次标定过程未完成或出现错误！");
    }
}

void MainWindow::handleBatchProgress(int channel, int progress)
{
    // Update progress bar
    calibrationDialog->getUi()->progressBar->setValue(progress);
}

void MainWindow::handleReconnectionRequired(const QVector<int> &channelsToConnect)
{
    // Format channels list for display
    QString channelList;
    for (int i = 0; i < channelsToConnect.size(); ++i)
    {
        if (i > 0)
            channelList += ", ";
        channelList += QString::number(channelsToConnect[i]);
    }

    // Show reconnection dialog
    QMessageBox reconnectBox(calibrationDialog);
    reconnectBox.setWindowTitle("通道连接");
    reconnectBox.setText(QString("请连接以下设备通道至1220：\n%1").arg(channelList));
    reconnectBox.setInformativeText("完成连接后请点击确定继续校准。");
    reconnectBox.setStandardButtons(QMessageBox::Ok);
    reconnectBox.setIcon(QMessageBox::Information);
    reconnectBox.adjustSize(); // 调整对话框大小以适应内容
    reconnectBox.exec();
}

void MainWindow::handleWaitingForUserConfirmation(int batchIndex)
{
    // This slot is triggered when batch calibration is waiting for user confirmation
    // You can implement custom UI behavior here, or use the reconnectionRequired dialog
}

void MainWindow::handleBatchStarting(int batchIndex, int totalBatches, const QVector<int> &channelsInBatch)
{
    // Update UI to show which batch is being calibrated
    QString message = QString("开始第 %1/%2 批校准，包含通道: ")
                          .arg(batchIndex)
                          .arg(totalBatches);

    for (int i = 0; i < channelsInBatch.size(); ++i)
    {
        if (i > 0)
            message += ", ";
        message += QString::number(channelsInBatch[i] + 1); // Convert to 1-based
    }

    handleLogMessage(message);
}

/*------------------------------------------------------------------------------*/
/*-----------------------------------校准---------------------------------------*/
void MainWindow::generateAdjTable(AdjDeviceConfig &device)
{
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj;

    // 清空表格
    table->clear();

    // 隐藏行号和列号
    table->verticalHeader()->setVisible(false);
    table->horizontalHeader()->setVisible(false);

    // 设置最小列宽
    table->horizontalHeader()->setMinimumSectionSize(88);

    // 设置滚动条策略
    table->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    table->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded); // 根据设备类型确定每个参考阻值的行数
    bool isTM18SeriesType = device.name.contains("TM18ND") || device.name.contains("TM18RD");
    int rowsPerRefValue = isTM18SeriesType ? 8 : 9; // TM18系列类型8行，其他类型9行

    int totalRows = device.ref_values.size() * rowsPerRefValue;
    int totalCols = device.num_channels + 1; // 参考电阻列 + 通道列

    // 设置表格行列数
    table->setColumnCount(totalCols);
    table->setRowCount(totalRows);

    // 遍历每个参考阻值，填充表格内容
    for (int refIdx = 0; refIdx < device.ref_values.size(); ++refIdx)
    {
        int startRow = refIdx * rowsPerRefValue;

        // 设置表头行
        QTableWidgetItem *headerRefItem = new QTableWidgetItem("参考电阻(Ω)");
        headerRefItem->setTextAlignment(Qt::AlignCenter);
        headerRefItem->setBackground(QColor(240, 240, 240));
        table->setItem(startRow, 0, headerRefItem);

        // 添加通道表头
        for (int ch = 0; ch < device.num_channels; ++ch)
        {
            QTableWidgetItem *headerChItem = new QTableWidgetItem(QString("CH%1").arg(ch + 1));
            headerChItem->setTextAlignment(Qt::AlignCenter);
            headerChItem->setBackground(QColor(240, 240, 240));
            table->setItem(startRow, ch + 1, headerChItem);
        }

        // 填充参考阻值
        QTableWidgetItem *refValueItem = new QTableWidgetItem(QString::number(device.ref_values[refIdx], 'f', 5));
        refValueItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 1, 0, refValueItem);

        // 合并参考阻值单元格（纵向合并4行）
        table->setSpan(startRow + 1, 0, 4, 1);

        // 为每个通道添加4行空白单元格
        for (int ch = 0; ch < device.num_channels; ++ch)
        {
            for (int i = 0; i < 4; ++i)
            {
                QTableWidgetItem *emptyItem = new QTableWidgetItem("");
                emptyItem->setTextAlignment(Qt::AlignCenter);
                table->setItem(startRow + 1 + i, ch + 1, emptyItem);
            }
        }

        // 填充测量阻值行
        QTableWidgetItem *measuredResistanceLabel = new QTableWidgetItem("测量阻值(Ω)");
        measuredResistanceLabel->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 5, 0, measuredResistanceLabel);

        // 为每个通道添加测量阻值单元格
        for (int ch = 0; ch < device.num_channels; ++ch)
        {
            QTableWidgetItem *measuredResistanceItem = new QTableWidgetItem("");
            measuredResistanceItem->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 5, ch + 1, measuredResistanceItem);
        }

        // 填充偏差行
        QTableWidgetItem *deviationLabel = new QTableWidgetItem("偏差(Ω)");
        deviationLabel->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 6, 0, deviationLabel);

        // 为每个通道添加偏差单元格
        for (int ch = 0; ch < device.num_channels; ++ch)
        {
            QTableWidgetItem *deviationItem = new QTableWidgetItem("");
            deviationItem->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 6, ch + 1, deviationItem);
        }
        // 根据设备类型确定表格结构
        bool isTM18SeriesType = device.name.contains("TM18ND") || device.name.contains("TM18RD");

        if (isTM18SeriesType)
        {
            // TM18系列类型：只有允许偏差(Ω)，没有等效温度偏差
            QTableWidgetItem *toleranceLabel = new QTableWidgetItem("允差(Ω)");
            toleranceLabel->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 7, 0, toleranceLabel);

            // 合并允差值单元格（横向合并所有通道列）
            table->setSpan(startRow + 7, 1, 1, device.num_channels);
        }
        else
        {
            // 标准类型：有允差(mK)和等效温度偏差(mK)
            QTableWidgetItem *toleranceLabel = new QTableWidgetItem("允差(mK)");
            toleranceLabel->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 7, 0, toleranceLabel);

            // 合并允差值单元格（横向合并所有通道列）
            table->setSpan(startRow + 7, 1, 1, device.num_channels);

            // 填充等效温度偏差行
            QTableWidgetItem *equivalentTempDeviationLabel = new QTableWidgetItem("等效温度偏差(mK)");
            equivalentTempDeviationLabel->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 8, 0, equivalentTempDeviationLabel);

            // 为每个通道添加等效温度偏差单元格
            for (int ch = 0; ch < device.num_channels; ++ch)
            {
                QTableWidgetItem *equivalentTempDeviationItem = new QTableWidgetItem("");
                equivalentTempDeviationItem->setTextAlignment(Qt::AlignCenter);
                table->setItem(startRow + 8, ch + 1, equivalentTempDeviationItem);
            }
        }
    }

    // 设置表格列宽自适应
    table->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 设置表格样式
    table->setStyleSheet("QTableWidget { gridline-color: #CCCCCC; }");
    table->setShowGrid(true);
}

// 生成1618A TC电压校准表格
void MainWindow::generateVoltageAdjTable()
{
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj_2;

    // 清空表格
    table->clear();

    // 隐藏行号和列号
    table->verticalHeader()->setVisible(false);
    table->horizontalHeader()->setVisible(false);

    // 设置最小列宽
    table->horizontalHeader()->setMinimumSectionSize(88);

    // 设置滚动条策略
    table->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    table->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 电压档位定义
    QVector<double> voltageValues = {-10.0, 10.0, 30.0, 50.0, 75.0};
    QStringList voltageDescriptions = {"-10mV", "10mV", "30mV", "50mV", "75mV"};

    int numChannels = 8;    // CH1-CH8
    int rowsPerVoltage = 8; // 与TM18系列相同结构：表头、参考电压、4轮测量值、测量电压、偏差、允差

    int totalRows = voltageValues.size() * rowsPerVoltage;
    int totalCols = numChannels + 1; // 参考电压列 + 通道列

    // 设置表格行列数
    table->setColumnCount(totalCols);
    table->setRowCount(totalRows);

    // 遍历每个电压档位，填充表格内容
    for (int voltIdx = 0; voltIdx < voltageValues.size(); ++voltIdx)
    {
        int startRow = voltIdx * rowsPerVoltage;

        // 设置表头行
        QTableWidgetItem *headerRefItem = new QTableWidgetItem("参考电压(mV)");
        headerRefItem->setTextAlignment(Qt::AlignCenter);
        headerRefItem->setBackground(QColor(240, 240, 240));
        table->setItem(startRow, 0, headerRefItem);

        // 添加通道表头
        for (int ch = 0; ch < numChannels; ++ch)
        {
            QTableWidgetItem *headerChItem = new QTableWidgetItem(QString("CH%1").arg(ch + 1));
            headerChItem->setTextAlignment(Qt::AlignCenter);
            headerChItem->setBackground(QColor(240, 240, 240));
            table->setItem(startRow, ch + 1, headerChItem);
        }

        // 填充参考电压值
        QTableWidgetItem *refVoltageItem = new QTableWidgetItem(voltageDescriptions[voltIdx]);
        refVoltageItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 1, 0, refVoltageItem);

        // 合并参考电压单元格（纵向合并4行）
        table->setSpan(startRow + 1, 0, 4, 1);

        // 为每个通道添加4行空白单元格（用于显示4轮测量值）
        for (int ch = 0; ch < numChannels; ++ch)
        {
            for (int i = 0; i < 4; ++i)
            {
                QTableWidgetItem *emptyItem = new QTableWidgetItem("");
                emptyItem->setTextAlignment(Qt::AlignCenter);
                table->setItem(startRow + 1 + i, ch + 1, emptyItem);
            }
        }

        // 填充测量电压行
        QTableWidgetItem *measuredVoltageLabel = new QTableWidgetItem("测量电压(mV)");
        measuredVoltageLabel->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 5, 0, measuredVoltageLabel);

        // 为每个通道添加测量电压单元格
        for (int ch = 0; ch < numChannels; ++ch)
        {
            QTableWidgetItem *measuredVoltageItem = new QTableWidgetItem("");
            measuredVoltageItem->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 5, ch + 1, measuredVoltageItem);
        }

        // 填充偏差行
        QTableWidgetItem *deviationLabel = new QTableWidgetItem("偏差(mV)");
        deviationLabel->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 6, 0, deviationLabel);

        // 为每个通道添加偏差单元格
        for (int ch = 0; ch < numChannels; ++ch)
        {
            QTableWidgetItem *deviationItem = new QTableWidgetItem("");
            deviationItem->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 6, ch + 1, deviationItem);
        }

        // 填充允差行（与TM18系列不同，允差行不合并）
        QTableWidgetItem *toleranceLabel = new QTableWidgetItem("允差(mV)");
        toleranceLabel->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 7, 0, toleranceLabel);

        // 为每个通道添加允差单元格（不合并）
        for (int ch = 0; ch < numChannels; ++ch)
        {
            QTableWidgetItem *toleranceItem = new QTableWidgetItem("");
            toleranceItem->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 7, ch + 1, toleranceItem);
        }
    }

    // 设置表格列宽自适应
    table->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 设置表格样式
    table->setStyleSheet("QTableWidget { gridline-color: #CCCCCC; }");
    table->setShowGrid(true);
}

void MainWindow::initializeVoltageCalibrationTable()
{
    if (!calibrationDialog->getUi()->readDataTable_2)
        return;

    QTableWidget *table = calibrationDialog->getUi()->readDataTable_2;

    // 清空表格
    table->clear();

    // 设置表格结构：只显示0mV和50mV两行数据
    // 表格结构：通道 | 参考电压(mV) | 测量电压(mV) | 电压偏差(mV) | 允许偏差(mV) | 校准结果
    table->setColumnCount(6);
    table->setRowCount(2); // 0mV数据行 + 50mV数据行

    QStringList headers = {"通道", "参考电压(mV)", "测量电压(mV)", "电压偏差(mV)", "允许偏差(mV)", "校准结果"};
    table->setHorizontalHeaderLabels(headers);

    // 设置表格属性
    table->verticalHeader()->setVisible(false);
    table->horizontalHeader()->setVisible(true);
    table->horizontalHeader()->setMinimumSectionSize(88);
    table->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    table->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 设置表头下框线样式
    table->setStyleSheet(
        "QTableWidget { gridline-color: #CCCCCC; }"
        "QHeaderView::section { "
        "    border: 1px solid #CCCCCC; "
        "    background-color: #F0F0F0; "
        "    padding: 4px; "
        "}");
    table->setShowGrid(true);

    // 定义样式颜色
    QColor headerBgColor(240, 240, 240);

    // 初始化0mV档位数据行
    int row0mV = 0;
    QTableWidgetItem *channel0Item = new QTableWidgetItem("CH1");
    channel0Item->setTextAlignment(Qt::AlignCenter);
    table->setItem(row0mV, 0, channel0Item);

    QTableWidgetItem *ref0Item = new QTableWidgetItem("0.0");
    ref0Item->setTextAlignment(Qt::AlignCenter);
    ref0Item->setBackground(QBrush(headerBgColor));
    table->setItem(row0mV, 1, ref0Item);

    // 其他列初始为空，等待校准完成后更新
    for (int col = 2; col < 6; col++)
    {
        QTableWidgetItem *emptyItem = new QTableWidgetItem("");
        emptyItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(row0mV, col, emptyItem);
    }

    // 初始化50mV档位数据行
    int row50mV = 1;
    QTableWidgetItem *channel50Item = new QTableWidgetItem("CH1");
    channel50Item->setTextAlignment(Qt::AlignCenter);
    table->setItem(row50mV, 0, channel50Item);

    QTableWidgetItem *ref50Item = new QTableWidgetItem("50.0");
    ref50Item->setTextAlignment(Qt::AlignCenter);
    ref50Item->setBackground(QBrush(headerBgColor));
    table->setItem(row50mV, 1, ref50Item);

    // 其他列初始为空，等待校准完成后更新
    for (int col = 2; col < 6; col++)
    {
        QTableWidgetItem *emptyItem = new QTableWidgetItem("");
        emptyItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(row50mV, col, emptyItem);
    }

    // 设置列宽自适应
    for (int i = 0; i < table->columnCount(); ++i)
    {
        table->horizontalHeader()->setSectionResizeMode(i, QHeaderView::Stretch);
    }
}

void MainWindow::addVoltageCalibrationSummary(bool success)
{
    // 不再需要汇总行，校准结果通过消息框显示
    // 表格只显示0mV和50mV两行的校准数据
}

void MainWindow::resetCalibrationDialogState()
{
    if (!calibrationDialog)
        return;

    // 重置校准模式标志
    calibrationDialog->setIsVoltageCalibrationMode(false);

    // 恢复按钮状态：启用电阻校准，禁用电压校准
    if (calibrationDialog->getUi()->startCal_CalDialog)
    {
        calibrationDialog->getUi()->startCal_CalDialog->setEnabled(true);
    }
    if (calibrationDialog->getUi()->startCal_CalDialog_2)
    {
        calibrationDialog->getUi()->startCal_CalDialog_2->setEnabled(false);
    }

    // 显示电阻校准组件，隐藏电压校准组件
    if (calibrationDialog->getUi()->groupBox_2)
    {
        calibrationDialog->getUi()->groupBox_2->setVisible(true); // 电阻校准组件
    }
    if (calibrationDialog->getUi()->groupBox_5)
    {
        calibrationDialog->getUi()->groupBox_5->setVisible(false); // 电压校准组件
    }

    // 清空电压校准表格
    if (calibrationDialog->getUi()->readDataTable_2)
    {
        calibrationDialog->getUi()->readDataTable_2->clear();
        calibrationDialog->getUi()->readDataTable_2->setRowCount(0);
    }

    // 清空电阻校准表格
    if (calibrationDialog->getUi()->readDataTable)
    {
        calibrationDialog->getUi()->readDataTable->clear();
        calibrationDialog->getUi()->readDataTable->setRowCount(0);
    }

    // 清空板卡信息
    if (calibrationDialog->getUi()->boardCardModelEdit_CalDialog)
    {
        calibrationDialog->getUi()->boardCardModelEdit_CalDialog->clear();
    }
    if (calibrationDialog->getUi()->boardCardNumberEdit_CalDialog)
    {
        calibrationDialog->getUi()->boardCardNumberEdit_CalDialog->clear();
    }

    qDebug() << "校准对话框状态已重置为电阻校准模式";
}

void MainWindow::startVerificationInThread(AdjDeviceConfig &device)
{
    // 显示校准对话框
    if (!verificationDialog->isVisible())
    {
        // verificationDialog->show();

        // 使用 exec() 以模态方式显示对话框
        verificationDialog->setWindowModality(Qt::ApplicationModal);
        verificationDialog->exec();
    }

    verificationDialog->getUi()->progressBar_Adj->setValue(0);

    // 生成校准表格
    generateAdjTable(device);

    const int thresholdForBatchCalibration = 16;
    if (device.num_channels > thresholdForBatchCalibration)
    {
        // Use batch calibration for devices with many channels
        startBatchVerificationInThread(device);
    }
    else
    {
        // 设置工作对象的设备配置
        verificationWorker->setDeviceConfig(device);
        verificationWorker->setBatchMode(false); // 16通道以下设备使用非批量模式

        //    if(verificationDialog->getUi()->startCal_Adj) {
        //        verificationDialog->getUi()->startCal_Adj->setEnabled(false);
        //    }

        // 发射信号开始校准 调用verificationWorker校准
        emit startCalibration2();
    }
}

void MainWindow::handleVerificationStarted()
{
    // 校准开始时的处理
    handleLogMessage_Adj("校准进程已启动");

    if (verificationDialog->getUi()->startCal_Adj)
    {
        verificationDialog->getUi()->startCal_Adj->setEnabled(false);
    }

    if (verificationDialog->getUi()->abortCal_Adj)
    {
        verificationDialog->getUi()->abortCal_Adj->setEnabled(true);
    }

    if (verificationDialog->getUi()->restartCal_Adj)
    {
        verificationDialog->getUi()->restartCal_Adj->setEnabled(false);
    }

    if (verificationDialog->getUi()->endCal_Adj)
    {
        verificationDialog->getUi()->endCal_Adj->setEnabled(false);
    }
}

void MainWindow::handleVerificationFinished(bool success)
{
    // 校准完成时的处理
    if (verificationDialog->getUi()->startCal_Adj)
    {
        verificationDialog->getUi()->startCal_Adj->setEnabled(true);
    }

    if (verificationDialog->getUi()->abortCal_Adj)
    {
        verificationDialog->getUi()->abortCal_Adj->setEnabled(false);
    }

    if (verificationDialog->getUi()->restartCal_Adj)
    {
        verificationDialog->getUi()->restartCal_Adj->setEnabled(true);
    }

    if (verificationDialog->getUi()->endCal_Adj)
    {
        verificationDialog->getUi()->endCal_Adj->setEnabled(true);
    }

    if (success)
    {
        // showAutoFadeInformation(verificationDialog, "成功", "校准过程已成功完成！");
        QMessageBox::information(verificationDialog, "完成", "校准过程已成功完成！");
    }
    else
    {
        verificationDialog->getUi()->progressBar_Adj->setValue(0); // 重置进度条
        // showAutoFadeInformation(verificationDialog, "警告", "校准过程未完成或出现错误！");
        QMessageBox::warning(verificationDialog, "警告", "校准过程未完成或出现错误！");
    }
}

void MainWindow::handleVerificationProgress(int channel, int progress)
{
    // 更新进度条
    verificationDialog->getUi()->progressBar_Adj->setValue(progress);
}

void MainWindow::handleLogMessage_Adj(const QString &message)
{
    // 获取当前日期时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString timestamp = currentDateTime.toString("yyyy/MM/dd hh:mm:ss");
    QString dateString = currentDateTime.toString("yyyyMMdd");

    // 将消息添加到日志文本框
    verificationDialog->getUi()->textEdit_Adj->append(QString("%1\n%2\n").arg(timestamp).arg(message));

    // 移动光标到文本框的末尾
    QTextCursor cursor = verificationDialog->getUi()->textEdit_Adj->textCursor();
    cursor.movePosition(QTextCursor::End);
    verificationDialog->getUi()->textEdit_Adj->setTextCursor(cursor);

    // 日志持久化保存
    // 构建日志目录路径
    QString appDir = QCoreApplication::applicationDirPath();
    QString logDirPath = QDir(appDir).filePath("Auto_TAdj_Log");

    // 检查并创建日志目录
    QDir logDir(logDirPath);
    if (!logDir.exists())
    {
        if (!logDir.mkpath("."))
        {
            qDebug() << "无法创建日志目录:" << logDirPath;
            return;
        }
    }

    // 构建日志文件名
    QString logFileName = QString("Auto_TAdj_Log_%1.log").arg(dateString);

    // 构建日志文件路径
    QString logFilePath = logDir.filePath(logFileName);

    // 打开日志文件进行追加写入
    QFile logFile(logFilePath);
    if (logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
    {
        QTextStream stream(&logFile);
        stream << timestamp << "\n"
               << message << "\n\n";
        logFile.close();
    }
    else
    {
        // 如果打开文件失败，可以在UI上显示错误信息
        qDebug() << "无法打开日志文件进行写入:" << logFile.errorString();
    }
}

// 更新UI表 传入参考阻值、通道号（1-n)、4轮阻值、均值、均值偏差、允许偏差、等效温度偏差(mK)、允许偏差结果
void MainWindow::handleUpdateChannelData_Adj(double referenceValue, int channel, QVector<double> resistances, double measuredResistance,
                                             double deviationFromReference, double allowedDeviation, double equivalentTempDeviation, bool calibrationResult)
{
    // qDebug() << "开始更新通道" << channel << "，时间:" << QTime::currentTime().toString();
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj;

    // 判断设备类型以确定行数步长
    QString deviceName = verificationDialog->getUi()->deviceModel_Adj->text();
    bool isTM18SeriesType = deviceName.contains("TM18ND") || deviceName.contains("TM18RD");
    int rowsPerRefValue = isTM18SeriesType ? 8 : 9;

    // Find the startRow for the reference value
    int startRow = -1;
    for (int row = 1; row < table->rowCount(); row += rowsPerRefValue)
    {
        QTableWidgetItem *item = table->item(row, 0);
        if (item && item->text() == QString::number(referenceValue, 'f', 5))
        {
            startRow = row - 1;
            break;
        }
    }
    if (startRow == -1)
    {
        qDebug() << "未找到参考值" << referenceValue << "，通道" << channel;
        return;
    }

    // Clear the entire column before updating, except for the 7th row
    for (int row = startRow; row < startRow + rowsPerRefValue; ++row)
    {
        if (row == startRow || row == startRow + 7)
        {
            continue; // Skip the header row and tolerance row
        }
        QTableWidgetItem *item = table->item(row, channel);
        if (item)
        {
            item->setText(""); // 清空内容
        }
    }

    // Update the four resistance values
    for (int i = 0; i < 4; ++i)
    {
        table->item(startRow + 1 + i, channel)->setText(QString::number(resistances[i], 'f', 5));
    }

    // Update measured resistance
    table->item(startRow + 5, channel)->setText(QString::number(measuredResistance, 'f', 5)); // Update deviation
    QTableWidgetItem *deviationItem = table->item(startRow + 6, channel);
    if (deviationItem)
    {
        deviationItem->setText(QString::number(deviationFromReference, 'f', 5));

        // 对TM18系列设备，在偏差行设置颜色（因为没有等效温度偏差行）
        if (isTM18SeriesType)
        {
            deviationItem->setForeground(calibrationResult ? Qt::green : Qt::red);
        }
    }

    // 此处省略更新允许偏差的值（需要确保在创建表格时的允差与worker计算出的允差结果保持一致）
    if (channel == 1)
    {
        qDebug() << "allowedDeviation: " << allowedDeviation;
        QTableWidgetItem *allowedDeviationItem = table->item(startRow + 7, 1);
        if (!allowedDeviationItem)
        {
            // 根据设备型号确定小数位数：TM18RD-P需要2位，TM18ND-P需要1位
            int precision = (deviceName.contains("TM18RD")) ? 2 : 1;
            QString deviationText = isTM18SeriesType ? QString::number(allowedDeviation, 'f', precision) : QString::number(allowedDeviation, 'f', 1);
            allowedDeviationItem = new QTableWidgetItem(deviationText);
            allowedDeviationItem->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 7, 1, allowedDeviationItem);
        }
        else
        {
            // 根据设备型号确定小数位数：TM18RD-P需要2位，TM18ND-P需要1位
            int precision = (deviceName.contains("TM18RD")) ? 2 : 1;
            QString deviationText = isTM18SeriesType ? QString::number(allowedDeviation, 'f', precision) : QString::number(allowedDeviation, 'f', 1);
            allowedDeviationItem->setText(deviationText);
        }
    }

    // Update equivalent temperature deviation for non-TM18Series devices
    if (!isTM18SeriesType)
    {
        QTableWidgetItem *equivItem = table->item(startRow + 8, channel);
        if (equivItem)
        {
            equivItem->setText(QString::number(equivalentTempDeviation, 'f', 1));
            // equivItem->setBackground(calibrationResult ? QColor(0, 255, 0) : QColor(255, 0, 0));
            equivItem->setForeground(calibrationResult ? Qt::green : Qt::red); // 设置字体颜色
        }
    }

    emit uiUpdateFinished();
    // qDebug() << "通道" << channel << "发出 uiUpdateFinished 信号，时间:" << QTime::currentTime().toString();
}

// 更新1618A TC电压校准表格数据
void MainWindow::handleUpdateVoltageTableData_Adj(int channel, int level, const QVector<double> &voltageReadings,
                                                  double avgVoltage, double deviation, double tolerance, bool passed)
{
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj_2;

    if (!table)
    {
        qDebug() << "电压校准表格不存在";
        return;
    }

    // 计算表格中的位置
    // 每个电压档位占8行：表头、参考电压、4轮测量值、测量电压、偏差、允差
    int rowsPerVoltage = 8;
    int startRow = level * rowsPerVoltage;

    // 更新4轮测量值
    for (int i = 0; i < voltageReadings.size() && i < 4; ++i)
    {
        QTableWidgetItem *readingItem = table->item(startRow + 1 + i, channel + 1);
        if (!readingItem)
        {
            readingItem = new QTableWidgetItem();
            readingItem->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 1 + i, channel + 1, readingItem);
        }
        readingItem->setText(QString::number(voltageReadings[i], 'f', 6));
    }

    // 更新测量电压值（平均值）
    QTableWidgetItem *avgVoltageItem = table->item(startRow + 5, channel + 1);
    if (!avgVoltageItem)
    {
        avgVoltageItem = new QTableWidgetItem();
        avgVoltageItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 5, channel + 1, avgVoltageItem);
    }
    avgVoltageItem->setText(QString::number(avgVoltage, 'f', 6));

    // 更新偏差值
    QTableWidgetItem *deviationItem = table->item(startRow + 6, channel + 1);
    if (!deviationItem)
    {
        deviationItem = new QTableWidgetItem();
        deviationItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 6, channel + 1, deviationItem);
    }
    deviationItem->setText(QString::number(deviation, 'f', 6));
    // 根据是否合格设置颜色
    deviationItem->setForeground(passed ? Qt::green : Qt::red);

    // 更新允差值
    QTableWidgetItem *toleranceItem = table->item(startRow + 7, channel + 1);
    if (!toleranceItem)
    {
        toleranceItem = new QTableWidgetItem();
        toleranceItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 7, channel + 1, toleranceItem);
    }
    toleranceItem->setText(QString::number(tolerance, 'f', 6));

    handleLogMessage_Adj(QString("更新CH%1通道%2档位数据: 平均电压=%3mV, 偏差=%4mV, 允差=%5mV, %6")
                             .arg(channel + 1)
                             .arg(level == 0 ? "-10mV" : level == 1 ? "10mV"
                                                     : level == 2   ? "30mV"
                                                     : level == 3   ? "50mV"
                                                                    : "75mV")
                             .arg(avgVoltage, 0, 'f', 6)
                             .arg(deviation, 0, 'f', 6)
                             .arg(tolerance, 0, 'f', 6)
                             .arg(passed ? "合格" : "不合格"));
}

//-------------分批校准
void MainWindow::startBatchVerificationInThread(AdjDeviceConfig &device)
{
    if (verificationDialog->getUi()->startCal_Adj)
    {
        verificationDialog->getUi()->startCal_Adj->setEnabled(false);
    }

    if (verificationDialog->getUi()->abortCal_Adj)
    {
        verificationDialog->getUi()->abortCal_Adj->setEnabled(true);
    }

    if (verificationDialog->getUi()->restartCal_Adj)
    {
        verificationDialog->getUi()->restartCal_Adj->setEnabled(false);
    }

    if (verificationDialog->getUi()->endCal_Adj)
    {
        verificationDialog->getUi()->endCal_Adj->setEnabled(false);
    }

    isBatchVerificationInProgress = true;

    // Start batch verification
    batchVerificationManager->startBatchVerification(device, calCommandHandler, deviceCommandHandler);
}

void MainWindow::handleBatchVerificationCompleted(bool success)
{
    isBatchVerificationInProgress = false;

    if (verificationDialog->getUi()->startCal_Adj)
    {
        verificationDialog->getUi()->startCal_Adj->setEnabled(true);
    }

    if (verificationDialog->getUi()->abortCal_Adj)
    {
        verificationDialog->getUi()->abortCal_Adj->setEnabled(false);
    }

    if (verificationDialog->getUi()->restartCal_Adj)
    {
        verificationDialog->getUi()->restartCal_Adj->setEnabled(true);
    }

    if (verificationDialog->getUi()->endCal_Adj)
    {
        verificationDialog->getUi()->endCal_Adj->setEnabled(true);
    }

    if (success)
    {
        QMessageBox::information(verificationDialog, "完成", "批次标定过程已成功完成！");
    }
    else
    {
        verificationDialog->getUi()->progressBar_Adj->setValue(0); // 重置进度条
        QMessageBox::warning(verificationDialog, "警告", "批次标定过程未完成或出现错误！");
    }
}

void MainWindow::handleBatchProgress_Adj(int channel, int progress)
{
    // Update progress bar
    verificationDialog->getUi()->progressBar_Adj->setValue(progress);
}

void MainWindow::handleReconnectionRequired_Adj(const QVector<int> &channelsToConnect, int referenceIndex) // referenceIndex 为当前参考阻值的下标
{
    // Format channels list for display
    QString channelList;
    for (int i = 0; i < channelsToConnect.size(); ++i)
    {
        if (i > 0)
            channelList += ", ";
        channelList += QString::number(channelsToConnect[i]);
    }

    // Show reconnection dialog with updated message for optimized flow
    QMessageBox reconnectBox(verificationDialog);
    reconnectBox.setWindowTitle("通道连接");

    QString message;
    message = QString("请连接以下设备通道至1220：\n%1").arg(channelList);
    reconnectBox.setText(message);
    reconnectBox.setInformativeText("完成连接后请点击确定继续校准。");
    reconnectBox.setStandardButtons(QMessageBox::Ok);
    reconnectBox.setIcon(QMessageBox::Information);
    reconnectBox.adjustSize(); // 调整对话框大小以适应内容
    reconnectBox.exec();
}

void MainWindow::handleBatchStarting_Adj(int batchIndex, int totalBatches, int referenceIndex, const QVector<int> &channelsInBatch)
{
    // Update UI to show which batch is being calibrated
    QString message = QString("开始第 %1/%2 批校准，包含通道: ")
                          .arg(batchIndex)
                          .arg(totalBatches);

    for (int i = 0; i < channelsInBatch.size(); ++i)
    {
        if (i > 0)
            message += ", ";
        message += QString::number(channelsInBatch[i] + 1); // Convert to 1-based
    }

    handleLogMessage(message);
}

// 获取项目基本信息
bool MainWindow::getProjectInfo(int projectId, QString &projectName, QString &deviceModel,
                                QString &serialNumber, QDateTime &calibrationDate)
{
    QString sql = QString("SELECT project_name, device_model, device_serial_number, calibration_date "
                          "FROM Projects WHERE project_id = %1")
                      .arg(projectId);
    auto queryResult = dbHandler->queryRecordsPro(sql);
    if (queryResult.first && !queryResult.second.isEmpty())
    {
        const auto &record = queryResult.second.first();
        projectName = record.value(0).toString();                                         // project_name
        deviceModel = record.value(1).toString();                                         // device_model
        serialNumber = record.value(2).toString();                                        // device_serial_number
        calibrationDate = QDateTime::fromString(record.value(3).toString(), Qt::ISODate); // calibration_date
        return true;
    }
    return false;
}

// 获取项目的所有参考阻值
QVector<ReferenceData> MainWindow::getProjectReferenceValues(int projectId)
{
    QVector<ReferenceData> result;
    QString sql = QString("SELECT reference_id, reference_name, reference_value "
                          "FROM ReferenceValues WHERE project_id = %1")
                      .arg(projectId);
    auto queryResult = dbHandler->queryRecordsPro(sql);
    if (queryResult.first)
    {
        for (const auto &record : queryResult.second)
        {
            ReferenceData refData;
            refData.referenceId = record.value(0).toInt();       // reference_id
            refData.referenceName = record.value(1).toString();  // reference_name
            refData.referenceValue = record.value(2).toDouble(); // reference_value
            refData.channels = getReferenceChannelData(refData.referenceId);
            result.append(refData);
        }
    }
    return result;
}

// 获取参考阻值的所有通道数据
QVector<ChannelData> MainWindow::getReferenceChannelData(int referenceId)
{
    QVector<ChannelData> result;
    QString sql = QString("SELECT channel_number, measured_resistance, deviation_from_reference, "
                          "equivalent_temp_deviation, allowed_deviation, calibration_result "
                          "FROM ChannelData WHERE reference_id = %1 ORDER BY channel_number")
                      .arg(referenceId);
    auto queryResult = dbHandler->queryRecordsPro(sql);
    if (queryResult.first)
    {
        for (const auto &record : queryResult.second)
        {
            ChannelData chData;
            chData.channelNumber = record.value(0).toInt();              // channel_number
            chData.measuredResistance = record.value(1).toDouble();      // measured_resistance
            chData.deviationFromReference = record.value(2).toDouble();  // deviation_from_reference
            chData.equivalentTempDeviation = record.value(3).toDouble(); // equivalent_temp_deviation
            chData.allowedDeviation = record.value(4).toDouble();        // allowed_deviation
            chData.calibrationResult = record.value(5).toBool();         // calibration_result
            result.append(chData);
        }
    }
    return result;
}

// 获取完整的项目数据
ProjectData MainWindow::getProject(int projectId)
{
    ProjectData project;
    project.projectId = projectId;
    if (getProjectInfo(projectId, project.projectName, project.deviceModel,
                       project.deviceSerialNumber, project.calibrationDate))
    {
        project.referenceValues = getProjectReferenceValues(projectId);
    }
    return project;
}

/*--------------------------------------------------------------------------*/
// 添加新项目，返回项目ID
int MainWindow::addProject(const ProjectData &projectData)
{
    RecordData data;
    data["project_name"] = projectData.projectName;
    data["device_model"] = projectData.deviceModel;

    data["device_type"] = projectData.deviceType;

    data["device_serial_number"] = projectData.deviceSerialNumber;
    data["calibration_date"] = projectData.calibrationDate;

    QJsonObject ambientInfoJson = projectData.ambientInfo.toJson();
    QJsonDocument ambientInfoDoc(ambientInfoJson);
    QString ambientInfoJsonString = ambientInfoDoc.toJson(QJsonDocument::Compact);
    data["calibration_environmentinfo"] = ambientInfoJsonString;

    QJsonObject calDeviceInfoJson;
    for (const auto &device : projectData.calDeviceInfo)
    {
        calDeviceInfoJson[device.type] = device.toJson();
    }
    QJsonDocument calDeviceInfoDoc(calDeviceInfoJson);
    QString calDeviceInfoJsonString = calDeviceInfoDoc.toJson(QJsonDocument::Compact);
    data["calibration_deviceinfo"] = calDeviceInfoJsonString;

    if (dbHandler->insertRecord("Projects", data))
    {
        // 获取最后插入的ID
        QString query = "SELECT last_insert_rowid()";
        QVariant lastId = dbHandler->getScalarValue(query);
        return lastId.toInt();
    }
    else
    {
        return -1;
    }
}

// 添加参考阻值，返回参考阻值ID
int MainWindow::addReferenceValue(int projectId, const QString &referenceName, double referenceValue)
{
    RecordData data;
    data["project_id"] = projectId;
    data["reference_name"] = referenceName;
    data["reference_value"] = referenceValue;

    if (dbHandler->insertRecord("ReferenceValues", data))
    {
        // 获取最后插入的ID
        QString query = "SELECT last_insert_rowid()";
        QVariant lastId = dbHandler->getScalarValue(query);
        return lastId.toInt();
    }
    else
    {
        return -1;
    }
}

// 添加通道数据
bool MainWindow::addChannelData(int referenceId, const ChannelData &data)
{
    RecordData recordData;
    recordData["reference_id"] = referenceId;
    recordData["channel_number"] = data.channelNumber;

    QJsonArray measured_resistances_array;
    for (double resistance : data.measuredResistances)
    {
        measured_resistances_array.append(resistance);
    }
    QJsonDocument measured_resistances_doc(measured_resistances_array);
    QString measured_resistances_json = measured_resistances_doc.toJson(QJsonDocument::Compact);
    recordData["measured_resistances"] = measured_resistances_json;

    recordData["measured_resistance"] = data.measuredResistance;
    recordData["deviation_from_reference"] = data.deviationFromReference;
    recordData["equivalent_temp_deviation"] = data.equivalentTempDeviation;
    recordData["allowed_deviation"] = data.allowedDeviation;
    recordData["calibration_result"] = data.calibrationResult;

    return dbHandler->insertRecord("ChannelData", recordData);
}

// 验证表格是否支持保存到数据库
bool MainWindow::validateTableData()
{
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj;

    // 根据设备类型确定最少行数
    QString deviceName = verificationDialog->getUi()->deviceModel_Adj->text();
    bool isTM18SeriesType = deviceName.contains("TM18ND") || deviceName.contains("TM18RD");
    int minRowsRequired = isTM18SeriesType ? 8 : 9;

    if (table->rowCount() >= minRowsRequired)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool MainWindow::saveProjectToDatabase(const ProjectData &projectData)
{
    if (!dbHandler->beginTransaction())
    {
        qDebug() << "Failed to begin transaction";
        return false;
    }

    try
    {
        // int projectId = addProject(projectData.projectName, projectData.deviceModel,projectData.deviceSerialNumber, projectData.calibrationDate);
        int projectId = addProject(projectData);
        if (projectId <= 0)
        {
            throw std::runtime_error("Failed to add project");
        }

        for (const ReferenceData &refData : projectData.referenceValues)
        {
            int refId = addReferenceValue(projectId, refData.referenceName, refData.referenceValue);
            if (refId <= 0)
            {
                throw std::runtime_error("Failed to add reference value");
            }

            for (const ChannelData &chData : refData.channels)
            {
                if (!addChannelData(refId, chData))
                {
                    throw std::runtime_error("Failed to add channel data");
                }
            }
        }

        if (!dbHandler->commitTransaction())
        {
            throw std::runtime_error("Failed to commit transaction");
        }
        return true;
    }
    catch (const std::exception &e)
    {
        dbHandler->rollbackTransaction();
        qDebug() << "Error saving project:" << e.what();
        return false;
    }
}

// 从UI表中提取ProjectData数据 部分数据依赖于UI组件值
ProjectData MainWindow::extractProjectDataFromTable()
{
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj;
    ProjectData projectData;

    QString modelName;
    QString serialNumber;
    if (ui->deviceModel_Cal->currentText() == "1618A-N" || ui->deviceModel_Cal->currentText() == "1618A-L")
    {
        modelName = verificationDialog->getUi()->boardCardModel_Combo_Adj->currentText();
        serialNumber = verificationDialog->getUi()->serialNumEdit_Board_Adj->text();
    }
    else
    {
        modelName = verificationDialog->getUi()->deviceModel_Adj->text();
        serialNumber = verificationDialog->getUi()->serialNumEdit_Adj->text();
    }

    projectData.deviceModel = modelName;

    projectData.deviceType = ui->deviceName_Cal->currentText(); // "高精度测温仪"

    projectData.deviceSerialNumber = serialNumber;
    projectData.calibrationDate = QDateTime::currentDateTime();
    projectData.projectName = QString("%1-%2").arg(projectData.deviceSerialNumber,
                                                   QDateTime::currentDateTime().toString("yyyyMMddHHmmss"));

    AmbientInfo ambientInfo = {
        verificationDialog->getUi()->ambientTemp->text(), // ambient_temp
        verificationDialog->getUi()->ambientHum->text(),  // ambient_hum
        verificationDialog->getUi()->ambientPre->text(),  // ambient_pre
        verificationDialog->getUi()->calibrator->text(),  // calibrator
        verificationDialog->getUi()->reportNum->text()    // report_num
    };
    projectData.ambientInfo = ambientInfo;

    QList<DeviceInfo> devices;
    if (verificationDialog->getUi()->tempBridgeCheck->isChecked())
    {
        DeviceInfo device;
        device.type = verificationDialog->getUi()->tempBridgeCheck->text();
        device.model = verificationDialog->getUi()->tempBridgeModel->currentText();
        device.serialNumber = verificationDialog->getUi()->tempBridgeSerialNum->text();
        device.calibrationDate = verificationDialog->getUi()->tempBridgeDateTimeEdit->dateTime().toString("yyyy-MM-dd");
        devices.append(device);
    }

    if (verificationDialog->getUi()->refResistorCheck->isChecked())
    {
        DeviceInfo device;
        device.type = verificationDialog->getUi()->refResistorCheck->text();
        device.model = verificationDialog->getUi()->refResistorModel->currentText();
        device.serialNumber = verificationDialog->getUi()->refResistorSerialNum->text();
        device.calibrationDate = verificationDialog->getUi()->refResistorDateTimeEdit->dateTime().toString("yyyy-MM-dd");
        devices.append(device);
    }

    if (verificationDialog->getUi()->voltSourceCheck->isChecked())
    {
        DeviceInfo device;
        device.type = verificationDialog->getUi()->voltSourceCheck->text();
        device.model = verificationDialog->getUi()->voltSourceModel->currentText();
        device.serialNumber = verificationDialog->getUi()->voltSourceSerialNum->text();
        device.calibrationDate = verificationDialog->getUi()->voltSourceDateTimeEdit->dateTime().toString("yyyy-MM-dd");
        devices.append(device);
    }

    if (verificationDialog->getUi()->refResistorCheck4->isChecked())
    {
        DeviceInfo device;
        device.type = verificationDialog->getUi()->refResistorCheck4->text();
        device.model = verificationDialog->getUi()->refResistorModel4->currentText();
        device.serialNumber = verificationDialog->getUi()->refResistorSerialNum4->text();
        device.calibrationDate = verificationDialog->getUi()->refResistorDateTimeEdit4->dateTime().toString("yyyy-MM-dd");
        devices.append(device);
    }

    if (verificationDialog->getUi()->refResistorCheck5->isChecked())
    {
        DeviceInfo device;
        device.type = verificationDialog->getUi()->refResistorCheck5->text();
        device.model = verificationDialog->getUi()->refResistorModel5->currentText();
        device.serialNumber = verificationDialog->getUi()->refResistorSerialNum5->text();
        device.calibrationDate = verificationDialog->getUi()->refResistorDateTimeEdit5->dateTime().toString("yyyy-MM-dd");
        devices.append(device);
    }

    projectData.calDeviceInfo = devices;

    // 根据设备类型确定每个参考阻值的行数
    bool isTM18SeriesType = modelName.contains("TM18ND") || modelName.contains("TM18RD");
    int rowsPerRefValue = isTM18SeriesType ? 8 : 9;

    int numRefs = table->rowCount() / rowsPerRefValue;
    for (int refIdx = 0; refIdx < numRefs; ++refIdx)
    {
        int startRow = refIdx * rowsPerRefValue;
        ReferenceData refData;
        refData.referenceName = QString("参考阻值%1").arg(refIdx + 1);
        refData.referenceValue = table->item(startRow + 1, 0)->text().toDouble();

        for (int ch = 0; ch < verificationDialog->getUi()->readDataTable_Adj->columnCount() - 1; ++ch)
        {
            ChannelData chData;
            chData.channelNumber = ch + 1;
            chData.referenceValue = refData.referenceValue;

            // 存储 row=1 到 row=4 的数据
            for (int row = 1; row <= 4; ++row)
            {
                QTableWidgetItem *item = table->item(startRow + row, ch + 1);
                double resistance = item ? item->text().toDouble() : 0.0;
                chData.measuredResistances.append(resistance);
            }

            QTableWidgetItem *measuredItem = table->item(startRow + 5, ch + 1);
            QTableWidgetItem *deviationItem = table->item(startRow + 6, ch + 1);
            QTableWidgetItem *allowedDevItem = table->item(startRow + 7, 1);

            chData.measuredResistance = measuredItem ? measuredItem->text().toDouble() : 0.0;
            chData.deviationFromReference = deviationItem ? deviationItem->text().toDouble() : 0.0;
            chData.allowedDeviation = allowedDevItem ? allowedDevItem->text().toDouble() : 0.0;

            if (isTM18SeriesType)
            {
                // TM18系列: 没有等效温度偏差行，直接使用阻值偏差进行判断
                chData.equivalentTempDeviation = 0.0; // 存储为0.0
                chData.calibrationResult = (qAbs(chData.deviationFromReference) <= chData.allowedDeviation);
            }
            else
            {
                // 标准设备: 使用等效温度偏差进行判断
                QTableWidgetItem *tempDevItem = table->item(startRow + 8, ch + 1);
                chData.equivalentTempDeviation = tempDevItem ? tempDevItem->text().toDouble() : 0.0;
                chData.calibrationResult = (qAbs(chData.equivalentTempDeviation) <= chData.allowedDeviation);
            }

            refData.channels.append(chData);
        }
        projectData.referenceValues.append(refData);
    }
    return projectData;
}

// 验证1618A TC电压校准表格数据
bool MainWindow::validateVoltageTableData()
{
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj_2;

    if (!table || table->rowCount() == 0 || table->columnCount() == 0)
    {
        return false;
    }

    // 检查是否有有效的数据
    bool hasValidData = false;
    int rowsPerVoltage = 8; // 每个电压档位8行
    int numVoltages = 5;    // 5个电压档位
    int numChannels = 8;    // 8个通道

    for (int voltIdx = 0; voltIdx < numVoltages; ++voltIdx)
    {
        int startRow = voltIdx * rowsPerVoltage;
        for (int ch = 0; ch < numChannels; ++ch)
        {
            // 检查测量电压值是否存在
            QTableWidgetItem *measuredItem = table->item(startRow + 5, ch + 1);
            if (measuredItem && !measuredItem->text().isEmpty())
            {
                hasValidData = true;
                break;
            }
        }
        if (hasValidData)
            break;
    }

    return hasValidData;
}

// 从1618A TC电压校准表格中提取ProjectData数据
ProjectData MainWindow::extractVoltageProjectDataFromTable()
{
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj_2;
    ProjectData projectData;

    QString modelName;
    QString serialNumber;
    if (ui->deviceModel_Cal->currentText() == "1618A-N" || ui->deviceModel_Cal->currentText() == "1618A-L")
    {
        modelName = verificationDialog->getUi()->boardCardModel_Combo_Adj->currentText();
        serialNumber = verificationDialog->getUi()->serialNumEdit_Board_Adj->text();
    }
    else
    {
        modelName = verificationDialog->getUi()->deviceModel_Adj->text();
        serialNumber = verificationDialog->getUi()->serialNumEdit_Adj->text();
    }

    projectData.deviceModel = modelName + " (电压校准)";        // 标记为电压校准
    projectData.deviceType = ui->deviceName_Cal->currentText(); // "高精度测温仪"
    projectData.deviceSerialNumber = serialNumber;
    projectData.calibrationDate = QDateTime::currentDateTime();
    projectData.projectName = QString("%1-电压校准-%2").arg(projectData.deviceSerialNumber, QDateTime::currentDateTime().toString("yyyyMMddHHmmss"));

    AmbientInfo ambientInfo = {
        verificationDialog->getUi()->ambientTemp->text(), // ambient_temp
        verificationDialog->getUi()->ambientHum->text(),  // ambient_hum
        verificationDialog->getUi()->ambientPre->text(),  // ambient_pre
        verificationDialog->getUi()->calibrator->text(),  // calibrator
        verificationDialog->getUi()->reportNum->text()    // report_num
    };
    projectData.ambientInfo = ambientInfo;

    // 获取校准设备信息（754校准器）
    QList<DeviceInfo> devices;
    DeviceInfo device;
    device.type = "电压校准器";
    device.model = "754";
    device.serialNumber = verificationDialog->getUi()->calibrator->text();
    device.calibrationDate = QDateTime::currentDateTime().toString("yyyy-MM-dd");
    devices.append(device);
    projectData.calDeviceInfo = devices;

    // 电压档位定义
    QVector<double> voltageValues = {-10.0, 10.0, 30.0, 50.0, 75.0};
    QStringList voltageDescriptions = {"-10mV", "10mV", "30mV", "50mV", "75mV"};

    int rowsPerVoltage = 8; // 每个电压档位8行
    int numChannels = 8;    // 8个通道

    for (int voltIdx = 0; voltIdx < voltageValues.size(); ++voltIdx)
    {
        int startRow = voltIdx * rowsPerVoltage;
        ReferenceData refData;
        refData.referenceName = QString("参考电压%1").arg(voltageDescriptions[voltIdx]);
        refData.referenceValue = voltageValues[voltIdx];

        for (int ch = 0; ch < numChannels; ++ch)
        {
            ChannelData chData;
            chData.channelNumber = ch + 1;
            chData.referenceValue = refData.referenceValue;

            // 存储4轮测量值
            for (int round = 1; round <= 4; ++round)
            {
                QTableWidgetItem *item = table->item(startRow + round, ch + 1);
                double voltage = item ? item->text().toDouble() : 0.0;
                chData.measuredResistances.append(voltage); // 复用字段存储电压值
            }

            QTableWidgetItem *measuredItem = table->item(startRow + 5, ch + 1);
            QTableWidgetItem *deviationItem = table->item(startRow + 6, ch + 1);
            QTableWidgetItem *toleranceItem = table->item(startRow + 7, ch + 1);

            chData.measuredResistance = measuredItem ? measuredItem->text().toDouble() : 0.0;
            chData.deviationFromReference = deviationItem ? deviationItem->text().toDouble() : 0.0;
            chData.allowedDeviation = toleranceItem ? toleranceItem->text().toDouble() : 0.0;

            // 电压校准没有等效温度偏差
            chData.equivalentTempDeviation = 0.0;
            chData.calibrationResult = (qAbs(chData.deviationFromReference) <= chData.allowedDeviation);

            refData.channels.append(chData);
        }

        projectData.referenceValues.append(refData);
    }

    return projectData;
}

// 保存UI表数据到数据库
void MainWindow::on_endCal_Adj_clicked()
{
    if (validateTableData())
    {
        ProjectData projectData = extractProjectDataFromTable();
        if (MainWindow::saveProjectToDatabase(projectData))
        {
            loadCalibrationHistory_Adj();
            // showAutoFadeInformation(verificationDialog, "成功", "项目数据已保存");
            QMessageBox::information(verificationDialog, "成功", "项目数据已保存");
        }
        else
        {
            QMessageBox::warning(verificationDialog, "错误", "保存项目数据失败");
        }
    }
    else
    {
        QMessageBox::warning(verificationDialog, "警告", "表格数据无效，无法保存");
    }
}

void MainWindow::handleSaveResultsRequested_Adj()
{
    on_endCal_Adj_clicked();
}
/*------------------------------------------------------------------*/
// 复校 一定要在开始校准之后执行 因为借用了m_deviceConfig中参数

void MainWindow::on_recalibrateButton_clicked()
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj;
    QList<QTableWidgetItem *> selectedItems = table->selectedItems();

    // 检查是否选中了单元格
    if (selectedItems.isEmpty())
    {
        QMessageBox::warning(verificationDialog, "提示", "请先选中一个单元格");
        return;
    }

    // 获取选中的单元格（假设每次只选中一个）
    QTableWidgetItem *selectedItem = selectedItems.first();
    int row = selectedItem->row();    // 选中单元格的行号
    int col = selectedItem->column(); // 选中单元格的列号

    // 根据设备类型确定每组的行数
    QString deviceName = verificationDialog->getUi()->deviceModel_Adj->text();
    bool isTM18SeriesType = deviceName.contains("TM18ND") || deviceName.contains("TM18RD");
    int rowsPerGroup = isTM18SeriesType ? 8 : 9; // TM18系列用8行，标准设备用9行

    // 计算所属组号和组内行号
    int groupIndex = row / rowsPerGroup; // 计算所属组号
    int rowInGroup = row % rowsPerGroup; // 计算组内的行号

    // 获取参考阻值（假设参考阻值在每组的第1行，第0列）
    QTableWidgetItem *refItem = table->item(groupIndex * rowsPerGroup + 1, 0);
    if (!refItem)
    {
        QMessageBox::warning(verificationDialog, "错误", "无法获取参考阻值");
        return;
    }
    double referenceValue = refItem->text().toDouble(); // 将参考阻值转换为数值

    // 通道号直接由列号确定（假设第1列对应通道1，第2列对应通道2，以此类推）
    int channel = col;

    // qDebug() << "referenceValue: " << referenceValue;
    // qDebug() << "channel: " << channel;

    if (verificationDialog->getUi()->startCal_Adj)
    {
        verificationDialog->getUi()->startCal_Adj->setEnabled(false);
    }

    if (verificationDialog->getUi()->abortCal_Adj)
    {
        verificationDialog->getUi()->abortCal_Adj->setEnabled(true);
    }

    if (verificationDialog->getUi()->restartCal_Adj)
    {
        verificationDialog->getUi()->restartCal_Adj->setEnabled(false);
    }

    if (verificationDialog->getUi()->endCal_Adj)
    {
        verificationDialog->getUi()->endCal_Adj->setEnabled(false);
    }

    // 根据通道数决定不同的复校方式
    if (batchVerificationManager && (ui->chNums_Cal->currentText().toInt() > 16))
    {
        isBatchVerificationInProgress = true;
        batchVerificationManager->recalibrateChannel(groupIndex, referenceValue, channel);
    }
    else
    {
        // 触发复校操作
        emit restartCalibration2(groupIndex, referenceValue, channel); // 当前参考电阻通道号、参考值、设备通道
    }
}

/*------------------------------------------------------------------*/

void MainWindow::on_exportBtn_Adj_clicked()
{
    int projectId = -1;

    QListWidgetItem *item = verificationDialog->getUi()->historyListWidget_Adj->currentItem();
    if (item)
    {
        projectId = item->data(Qt::UserRole).toInt();
        // 处理选中的项目
    }
    else
    {
        QMessageBox::warning(verificationDialog, "提示", "请先选择一个项目！");
        return;
    }

    ProjectData projectData = getProjectData(projectId);

    // 获取用户指定的保存路径
    QString defaultFileName = projectData.deviceSerialNumber + "-" +
                              projectData.calibrationDate.toString("yyyyMMddHHmmss") + ".pdf";
    QString savePath = QFileDialog::getSaveFileName(verificationDialog, "保存报告",
                                                    "../" + defaultFileName,
                                                    "PDF文档 (*.pdf);;Word文档 (*.docx)");

    if (savePath.isEmpty())
    {
        return; // 用户取消了保存
    }

    // 创建并显示进度对话框
    progressDialog = new QProgressDialog("正在生成报告...", "取消", 0, 0, verificationDialog);
    progressDialog->setWindowModality(Qt::WindowModal);
    progressDialog->setCancelButton(nullptr);
    progressDialog->setWindowTitle("处理中...");
    progressDialog->setWindowFlags(progressDialog->windowFlags() & ~Qt::WindowContextHelpButtonHint);

    // 获取进度条对象并设置其样式
    QProgressBar *bar = progressDialog->findChild<QProgressBar *>();
    if (bar)
    {
        bar->setStyleSheet("QProgressBar { margin: 0px; padding: 0px; }");
        bar->setTextVisible(false); // 隐藏进度条上的文本显示
    }

    progressDialog->show();

    ExportTask *worker = new ExportTask(savePath, projectData);
    worker->setAutoDelete(false); // 禁用自动删除，手动管理对象生命周期 防止程序崩溃

    connect(worker, &ExportTask::progress, this, &MainWindow::updateProgress);
    connect(worker, &ExportTask::finished, this, &MainWindow::onReportExportFinished);
    connect(worker, &ExportTask::finished, [worker](bool, const QString &)
            { worker->deleteLater(); });

    QThreadPool::globalInstance()->start(worker);
}

void MainWindow::updateProgress(int value)
{
    if (progressDialog)
    {
        progressDialog->setValue(value);
    }
}

void MainWindow::closeProgressDialog()
{
    if (progressDialog)
    {
        progressDialog->close();
        progressDialog->deleteLater();
        progressDialog = nullptr;
    }
}

void MainWindow::onReportExportFinished(bool success, const QString &filePath)
{
    closeProgressDialog(); // 清除进度条

    if (success)
    {
        QMessageBox::information(verificationDialog, "导出成功", "报告已成功导出到:\n" + filePath, QMessageBox::Ok);
    }
    else
    {
        QMessageBox::critical(verificationDialog, "导出失败", "报告导出过程中发生错误", QMessageBox::Ok);
    }
}

/*------------------------------------------------------------------*/
// 双击项目历史记录
void MainWindow::fillTableWithProjectData(const ProjectData &projectData)
{
    QTableWidget *table = verificationDialog->getUi()->readDataTable_Adj;
    table->clear();

    // 隐藏行号和列号
    table->verticalHeader()->setVisible(false);
    table->horizontalHeader()->setVisible(false);

    // 设置滚动条策略
    table->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    table->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 根据设备类型确定行数
    bool isTM18SeriesType = projectData.deviceModel.contains("TM18ND") || projectData.deviceModel.contains("TM18RD");
    int rowsPerRefValue = isTM18SeriesType ? 8 : 9;

    table->setRowCount(projectData.referenceValues.size() * rowsPerRefValue);
    if (projectData.referenceValues.isEmpty())
    {
        table->setColumnCount(0);
        return;
    }
    int columnCount = projectData.referenceValues[0].channels.size() + 1;
    table->setColumnCount(columnCount);

    for (int refIdx = 0; refIdx < projectData.referenceValues.size(); ++refIdx)
    {
        const ReferenceData &refData = projectData.referenceValues[refIdx];
        int startRow = refIdx * rowsPerRefValue;

        // 设置表头行
        QTableWidgetItem *headerRefItem = new QTableWidgetItem("参考电阻(Ω)");
        headerRefItem->setTextAlignment(Qt::AlignCenter);
        headerRefItem->setBackground(QColor(240, 240, 240));
        table->setItem(startRow, 0, headerRefItem);

        // 添加通道表头
        for (int ch = 0; ch < refData.channels.size(); ++ch)
        {
            QTableWidgetItem *headerChItem = new QTableWidgetItem(QString("CH%1").arg(ch + 1));
            headerChItem->setTextAlignment(Qt::AlignCenter);
            headerChItem->setBackground(QColor(240, 240, 240));
            table->setItem(startRow, ch + 1, headerChItem);
        }

        // 填充参考阻值
        QTableWidgetItem *refValueItem = new QTableWidgetItem(QString::number(refData.referenceValue, 'f', 5));
        refValueItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 1, 0, refValueItem);

        // 合并参考阻值单元格（纵向合并4行）
        table->setSpan(startRow + 1, 0, 4, 1);

        // 为每个通道添加4行空白单元格
        for (int ch = 0; ch < refData.channels.size(); ++ch)
        {
            for (int i = 0; i < 4; ++i)
            {
                QTableWidgetItem *emptyItem = new QTableWidgetItem("");
                emptyItem->setTextAlignment(Qt::AlignCenter);
                table->setItem(startRow + 1 + i, ch + 1, emptyItem);
            }
        }

        // 填充测量阻值行
        QTableWidgetItem *measuredResistanceLabel = new QTableWidgetItem("测量阻值(Ω)");
        measuredResistanceLabel->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 5, 0, measuredResistanceLabel);

        // 为每个通道添加测量阻值单元格
        for (int ch = 0; ch < refData.channels.size(); ++ch)
        {
            // 填充measured_resistances的4个值
            for (int i = 0; i < 4; ++i)
            {
                QTableWidgetItem *measuredResistanceSubItem = new QTableWidgetItem(QString::number(refData.channels[ch].measuredResistances[i], 'f', 5));
                measuredResistanceSubItem->setTextAlignment(Qt::AlignCenter);
                table->setItem(startRow + 1 + i, ch + 1, measuredResistanceSubItem);
            }

            QTableWidgetItem *measuredResistanceItem = new QTableWidgetItem(QString::number(refData.channels[ch].measuredResistance, 'f', 5));
            measuredResistanceItem->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 5, ch + 1, measuredResistanceItem);
        }

        // 填充偏差行
        QTableWidgetItem *deviationLabel = new QTableWidgetItem("偏差(Ω)");
        deviationLabel->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 6, 0, deviationLabel);

        // 为每个通道添加偏差单元格
        for (int ch = 0; ch < refData.channels.size(); ++ch)
        {
            QTableWidgetItem *deviationItem = new QTableWidgetItem(QString::number(refData.channels[ch].deviationFromReference, 'f', 5));
            deviationItem->setTextAlignment(Qt::AlignCenter);

            // 对TM18系列设备，在偏差行设置颜色（因为没有等效温度偏差行）
            if (isTM18SeriesType)
            {
                deviationItem->setForeground(refData.channels[ch].calibrationResult ? Qt::green : Qt::red);
            }

            table->setItem(startRow + 6, ch + 1, deviationItem);
        }

        // 填充允差行
        QString toleranceLabel = isTM18SeriesType ? "允许偏差(Ω)" : "允差(mK)";
        QTableWidgetItem *toleranceLabelItem = new QTableWidgetItem(toleranceLabel);
        toleranceLabelItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 7, 0, toleranceLabelItem);

        // 获取允差值（根据参考阻值设定）
        double tolerance = refData.channels[0].allowedDeviation;
        QString toleranceFormat;
        if (isTM18SeriesType)
        {
            // 根据设备型号确定小数位数：TM18RD-P需要2位，TM18ND-P需要1位
            int precision = (projectData.deviceModel.contains("TM18RD")) ? 2 : 1;
            toleranceFormat = QString::number(tolerance, 'f', precision);
        }
        else
        {
            toleranceFormat = QString::number(tolerance);
        }

        QTableWidgetItem *toleranceValue = new QTableWidgetItem(toleranceFormat);
        toleranceValue->setTextAlignment(Qt::AlignCenter);
        table->setItem(startRow + 7, 1, toleranceValue);

        // 合并允差值单元格（横向合并所有通道列）
        table->setSpan(startRow + 7, 1, 1, refData.channels.size());

        // 只有非TM18系列设备才填充等效温度偏差行
        if (!isTM18SeriesType)
        {
            // 填充等效温度偏差行
            QTableWidgetItem *equivalentTempDeviationLabel = new QTableWidgetItem("等效温度偏差(mK)");
            equivalentTempDeviationLabel->setTextAlignment(Qt::AlignCenter);
            table->setItem(startRow + 8, 0, equivalentTempDeviationLabel);

            // 为每个通道添加等效温度偏差单元格
            for (int ch = 0; ch < refData.channels.size(); ++ch)
            {
                QTableWidgetItem *equivalentTempDeviationItem = new QTableWidgetItem(QString::number(refData.channels[ch].equivalentTempDeviation, 'f', 1));
                equivalentTempDeviationItem->setTextAlignment(Qt::AlignCenter);
                equivalentTempDeviationItem->setForeground(refData.channels[ch].calibrationResult ? Qt::green : Qt::red);
                table->setItem(startRow + 8, ch + 1, equivalentTempDeviationItem);
            }
        }
    }

    // 设置表格列宽自适应
    table->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 设置表格样式
    table->setStyleSheet("QTableWidget { gridline-color: #CCCCCC; }");
    table->setShowGrid(true);
}

QVector<double> MainWindow::convertJsonToMeasuredResistances(const QString &json)
{
    QVector<double> measuredResistances;

    // 将 JSON 字符串解析为 QJsonDocument
    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8());

    // 检查解析是否成功
    if (!doc.isNull() && doc.isArray())
    {
        QJsonArray jsonArray = doc.array();

        // 将 QJsonArray 中的每个元素转换为 double 并添加到 QVector<double> 中
        for (const QJsonValue &value : jsonArray)
        {
            measuredResistances.append(value.toDouble());
        }
    }

    return measuredResistances;
}

ProjectData MainWindow::getProjectData(int projectId)
{
    ProjectData projectData;
    projectData.projectId = projectId;

    // 获取项目基本信息
    // QString query = QString("SELECT project_name, device_model, device_serial_number, calibration_date FROM Projects WHERE project_id = %1").arg(projectId);
    QString query = QString("SELECT project_name, device_model, device_type, device_serial_number, calibration_date, "
                            "calibration_environmentinfo, calibration_deviceinfo FROM Projects WHERE project_id = %1")
                        .arg(projectId);
    auto result = dbHandler->queryRecordsPro(query);

    if (result.first && !result.second.isEmpty())
    {
        auto record = result.second.first();
        projectData.projectName = record.value(0).toString();
        projectData.deviceModel = record.value(1).toString();
        projectData.deviceType = record.value(2).toString();
        projectData.deviceSerialNumber = record.value(3).toString();
        projectData.calibrationDate = QDateTime::fromString(record.value(4).toString(), Qt::ISODate);

        // 解析 ambientInfo（存储为 JSON 字符串）
        QString ambientInfoJsonString = record.value(5).toString();
        QJsonDocument ambientInfoDoc = QJsonDocument::fromJson(ambientInfoJsonString.toUtf8());
        if (!ambientInfoDoc.isNull())
        {
            QJsonObject ambientInfoJson = ambientInfoDoc.object();
            projectData.ambientInfo = AmbientInfo::fromJson(ambientInfoJson);
        }
        else
        {
            qDebug() << "Failed to parse ambientInfo JSON for projectId:" << projectId;
        }

        // 解析 calDeviceInfo（存储为 JSON 字符串）
        QString calDeviceInfoJsonString = record.value(6).toString();
        QJsonDocument calDeviceInfoDoc = QJsonDocument::fromJson(calDeviceInfoJsonString.toUtf8());
        if (!calDeviceInfoDoc.isNull())
        {
            QJsonObject calDeviceInfoJson = calDeviceInfoDoc.object();
            for (const QString &key : calDeviceInfoJson.keys())
            {
                QJsonObject deviceJson = calDeviceInfoJson[key].toObject();
                DeviceInfo device = DeviceInfo::fromJson(deviceJson, key);
                projectData.calDeviceInfo.append(device);
            }
        }
        else
        {
            qDebug() << "Failed to parse calDeviceInfo JSON for projectId:" << projectId;
        }
    }

    // 获取项目的所有参考阻值及其通道数据
    query = QString("SELECT reference_id, reference_name, reference_value FROM ReferenceValues WHERE project_id = %1").arg(projectId);
    result = dbHandler->queryRecordsPro(query);

    for (const auto &record : result.second)
    {
        ReferenceData refData;
        refData.referenceId = record.value(0).toInt();
        refData.referenceName = record.value(1).toString();
        refData.referenceValue = record.value(2).toDouble();

        // 获取该参考阻值的所有通道数据
        QString chQuery = QString("SELECT channel_number, measured_resistance, deviation_from_reference, equivalent_temp_deviation, allowed_deviation, calibration_result, measured_resistances FROM ChannelData WHERE reference_id = %1").arg(refData.referenceId);
        auto chResult = dbHandler->queryRecordsPro(chQuery);

        for (const auto &chRecord : chResult.second)
        {
            ChannelData channelData;
            channelData.channelNumber = chRecord.value(0).toInt();
            // 将QString Json 的 measuredResistances转回QVecotr<double>格式
            QString measuredResistancesJson = chRecord.value(6).toString();
            channelData.measuredResistances = convertJsonToMeasuredResistances(measuredResistancesJson);

            channelData.measuredResistance = chRecord.value(1).toDouble();
            channelData.deviationFromReference = chRecord.value(2).toDouble();
            channelData.equivalentTempDeviation = chRecord.value(3).toDouble();
            channelData.allowedDeviation = chRecord.value(4).toDouble();
            channelData.calibrationResult = chRecord.value(5).toBool();
            refData.channels.append(channelData);
        }

        projectData.referenceValues.append(refData);
    }

    return projectData;
}

void MainWindow::loadProjectData(int projectId)
{
    ProjectData projectData = getProjectData(projectId);
    fillTableWithProjectData(projectData);
}

void MainWindow::onHistoryItemDoubleClicked_Adj(QListWidgetItem *item)
{
    int projectId = item->data(Qt::UserRole).toInt();
    // loadProjectData(projectId);

    ProjectData projectData = getProjectData(projectId);

    if (projectDetailsDialog)
    {
        delete projectDetailsDialog;
    }
    projectDetailsDialog = new ProjectDetailsDialog(verificationDialog);

    projectDetailsDialog->displayProjectData(projectData);

    projectDetailsDialog->exec();
}

void MainWindow::loadCalibrationHistory_Adj()
{
    if (dbHandler->isDatabaseConnected())
    {
        verificationDialog->getUi()->historyListWidget_Adj->clear();

        QString query = "SELECT project_id, project_name FROM Projects ORDER BY project_id ASC";
        auto result = dbHandler->queryRecordsPro(query);

        for (const auto &record : result.second)
        {
            int projectId = record.value(0).toInt();
            QString projectName = record.value(1).toString();
            QListWidgetItem *item = new QListWidgetItem(projectName);
            item->setData(Qt::UserRole, projectId);
            verificationDialog->getUi()->historyListWidget_Adj->addItem(item);
        }
    }
    else
    {
        // qDebug() << "数据库连接出错";
        handleLogMessage("数据库连接出错");
    }
}

/*------------------------------------------------------------------*/

// 删除项目
bool MainWindow::deleteProject(int projectId)
{
    return dbHandler->deleteRecord("Projects", QString("project_id = %1").arg(projectId)); // 由于外键约束 联动删除其余两表数据
}

void MainWindow::onHistoryDelBtnClicked_Adj()
{
    QListWidgetItem *item = verificationDialog->getUi()->historyListWidget_Adj->currentItem();
    if (item)
    {
        int projectId = item->data(Qt::UserRole).toInt();
        if (QMessageBox::question(verificationDialog, "确认", "确定要删除该项目吗？") == QMessageBox::Yes)
        {
            if (deleteProject(projectId))
            {
                delete item;
                loadCalibrationHistory_Adj();
                QMessageBox::information(verificationDialog, "成功", "项目已删除");
            }
            else
            {
                QMessageBox::warning(verificationDialog, "错误", "删除项目失败");
            }
        }
    }
    else
    {
        QMessageBox::warning(verificationDialog, "错误", "请选择要删除的项目");
    }
}

void MainWindow::on_historyListWidget_Adj_customContextMenuRequested(const QPoint &pos)
{
    QMenu contextMenu;

    // 使用Qt内置的标准删除图标
    QIcon deleteIcon = style()->standardIcon(QStyle::SP_TrashIcon);
    QAction *deleteAction = contextMenu.addAction(deleteIcon, "删除");

    QAction *selectedAction = contextMenu.exec(verificationDialog->getUi()->historyListWidget_Adj->mapToGlobal(pos));
    if (selectedAction == deleteAction)
    {
        QListWidgetItem *item = verificationDialog->getUi()->historyListWidget_Adj->currentItem();
        if (item)
        {
            int projectId = item->data(Qt::UserRole).toInt();
            if (QMessageBox::question(verificationDialog, "确认", "确定要删除该项目吗？") == QMessageBox::Yes)
            {
                if (deleteProject(projectId))
                {
                    delete item;
                    loadCalibrationHistory_Adj();
                    QMessageBox::information(verificationDialog, "成功", "项目已删除");
                }
                else
                {
                    QMessageBox::warning(verificationDialog, "错误", "删除项目失败");
                }
            }
        }
    }
}

void MainWindow::showAutoFadeInformation(QWidget *parent, const QString &title, const QString &message)
{
    QMessageBox *msgBox = new QMessageBox(QMessageBox::Information, title, message, QMessageBox::NoButton, parent);
    msgBox->setAttribute(Qt::WA_DeleteOnClose); // 确保消息框关闭后自动删除

    // 显示消息框
    msgBox->show();

    // 创建一个 QTimer，在指定时间后关闭消息框
    QTimer::singleShot(3000, msgBox, [msgBox]()
                       {
        // 创建 QPropertyAnimation 实现渐隐效果
        QPropertyAnimation* animation = new QPropertyAnimation(msgBox, "windowOpacity");
        animation->setDuration(1000); // 渐隐持续时间
        animation->setStartValue(1.0);
        animation->setEndValue(0.0);

        // 连接动画结束信号到消息框的关闭槽
        QObject::connect(animation, &QPropertyAnimation::finished, msgBox, &QMessageBox::close);

        // 开始动画
        animation->start(QAbstractAnimation::DeleteWhenStopped); });
}

void MainWindow::abortVerification()
{
    // 中止校准过程
    int ret = QMessageBox::question(verificationDialog, "确认", "确定要中止校准过程吗？",
                                    QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes)
    {
        if (isBatchVerificationInProgress)
        {
            batchVerificationManager->abortVerification();
        }
        else
        {
            verificationWorker->abortVerification();
        }
    }
}

void MainWindow::on_signal_startCal_AdjDialog(const QString &deviceModel, const QString &featureCode)
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    if (featureCode == "startCal")
    { // 传参：设备型号、功能码
        // 检查透传模式要求
        if (!checkTransparentModeRequirement(deviceModel, "开始校准"))
        {
            return;
        }

        AdjDeviceConfig device;
        device.name = deviceModel; // 1618A-N/1618A-L型号中与传入CalibrationWorker中的"1618A-N-NTC-01"或"1618A-L-NTC-01"格式不同，直接传入"1618A-N"或"1618A-L"
        device.num_channels = ui->chNums_Cal->currentText().toInt();

        // device.ref_values = {1000.012, 10000.312, 20000.110};

        // 根据设备型号 + 参考电阻计算允许偏差

        // 如果都没有checked 则弹窗提示其至少勾选一个，且referRVal1/2/3->text()的值要有效
        bool hasReference = false;

        QVector<QLineEdit *> refInputs = {ui->referRVal1, ui->referRVal2, ui->referRVal3, ui->referRVal4, ui->referRVal5};
        QVector<QCheckBox *> refChecks = {ui->refer1, ui->refer2, ui->refer3, ui->refer4, ui->refer5};
        QVector<QComboBox *> refChannels = {ui->referCH1, ui->referCH2, ui->referCH3, ui->referCH4, ui->referCH5};

        for (int i = 0; i < refChecks.size(); ++i)
        {
            if (refChecks[i]->isChecked())
            {
                hasReference = true;

                // device.ref_index.push_back(i + 1);

                int channelIndex = refChannels[i]->currentIndex() + 1; // 索引从1开始
                device.ref_index.push_back(channelIndex);

                QString text = refInputs[i]->text();
                if (text.isEmpty())
                {
                    QMessageBox::warning(verificationDialog, "提示", QString("参考电阻%1的值不能为空").arg(i + 1));
                    return;
                }
                bool ok;
                double val = text.toDouble(&ok);
                if (!ok)
                {
                    QMessageBox::warning(verificationDialog, "提示", QString("参考电阻%1的值无效").arg(i + 1));
                    return;
                }
                QString valStr = QString::number(val, 'f', 5);
                double valRounded = valStr.toDouble();

                device.ref_values.push_back(valRounded);
                // device.ref_values.push_back(val);
            }
        }

        if (!hasReference)
        {
            QMessageBox::warning(verificationDialog, "提示", "请至少勾选一个参考电阻");
            return;
        }
        // 校准并未用到写地址
        QMap<QString, DeviceFirstAddress> deviceFirstAddressConfig;
        deviceFirstAddressConfig["618A RTD"] = {0X0225, 0X0059};
        deviceFirstAddressConfig["618A RTD PLUS"] = {0X0225, 0X0059};
        deviceFirstAddressConfig["619A RTD PLUS"] = {0X0225, 0X0059};
        QStringList _618NTCModels = {"618A", "618A PLUS", "618A PLUS(6NTC+2P-AP23)", "619A PLUS",
                                     "TM14RD-PT100", "TM14RD-PT1000", "TM14ND", "TM14ND-T", "TM14ND-P", "TM14ND-P-S",
                                     "H-LCW-22B",
                                     "TM222ND-P", "TM224ND-P", "TM228ND-P"};
        for (const QString &model : _618NTCModels)
        {
            deviceFirstAddressConfig[model] = {0X01A5, 0X0059};
        }

        deviceFirstAddressConfig["618A NTC-32"] = {0X0325, 0X0099};
        deviceFirstAddressConfig["618A NTC-32-TIME"] = {0X0325, 0X00D9};
        deviceFirstAddressConfig["619A NTC-32 PLUS"] = {0X0325, 0X0099};
        deviceFirstAddressConfig["619A RTD-32 PLUS"] = {0X0425, 0X0099};

        deviceFirstAddressConfig["TM18ND-P"] = {0X014D, 0X0061};
        deviceFirstAddressConfig["TM18RD-P"] = {0X014D, 0X0061};
        deviceFirstAddressConfig["TM24ND-P-S"] = {0X01A4, 0X0058};

        deviceFirstAddressConfig["1611A-HT(PT100)"] = {0X0152, 0X0034};
        deviceFirstAddressConfig["1611A-HT(PT1000)"] = {0X0152, 0X0034};

        // deviceFirstAddressConfig["1618A-N"] = {0X0441, 0X003D};
        // deviceFirstAddressConfig["1618A-L"] = {0X00F9, 0X015D};

        if (ui->deviceModel_Cal->currentText() == "1618A-N" || ui->deviceModel_Cal->currentText() == "1618A-L")
        { // 1618A-N和1618A-L的板卡读写地址不根据CalibrationDialog传入的设备型号格式来设置
            if (ui->deviceModel_Cal->currentText() == "1618A-L")
            {
                device.write_addr = 0X00F9; // 1618A-L写入地址
                device.read_addr = 0X015D;  // 1618A-L读取地址
            }
            else // 1618A-N
            {
                device.write_addr = 0X0441; // 1618A-N写入地址
                device.read_addr = 0X003D;  // 1618A-N读取地址
            }
        }
        else if (deviceFirstAddressConfig.contains(deviceModel))
        {
            device.write_addr = deviceFirstAddressConfig[deviceModel].write_addr;
            device.read_addr = deviceFirstAddressConfig[deviceModel].read_addr;
        }

        // 8 -- {13, 14, 15, 16, 17, 18, 19, 20}
        device.cal_to_1220.resize(device.num_channels);
        int start_value = 21 - device.num_channels;

        for (int i = 0; i < device.num_channels; ++i)
        {
            device.cal_to_1220[i] = start_value + i;
        }

        // 设置开关切换等待时间，从UI获取（假设已存在ui->switchDelay组合框）
        device.switchDelay = ui->switchDelay ? ui->switchDelay->currentText().toInt() : 30; // 默认30秒

        startVerificationInThread(device);
    }
    else if (featureCode == "endCal")
    {
        // abortCalibration();
    }
}

void MainWindow::on_signal_readCommand_AdjDialog(const QString &deviceModel, const QString &featureCode)
{
    bool isConnected = checkConnection();
    if (!isConnected)
        return;

    QPair<bool, QString> result;

    if (featureCode == "readNum")
    {
        handleLogMessage_Adj("开始读取设备序列号...");

        // 检查是否为透传模式设备
        if (transparentModeDevices.contains(deviceModel))
        {
            if (isWakeUp)
            {
                // 检查设备型号是否一致
                if (currentTransparentDevice != deviceModel)
                {
                    handleLogMessage_Adj("检测到设备型号不一致，自动重置透传模式状态");
                    resetTransparentModeState();
                }
                else
                {
                    // 已唤醒状态：不允许读写序列号
                    handleLogMessage_Adj("设备已进入透传模式，不能读取序列号");
                    QMessageBox::warning(verificationDialog, "提示", "设备已进入透传模式，请先退出透传模式再读取序列号！");
                    return;
                }
            }

            // 未唤醒状态：使用字符串格式读取序列号
            handleLogMessage_Adj("使用字符串格式读取设备序列号...");
            QString msg = "SYST:SERIAL?\r\n";
            QByteArray sendData = msg.toLatin1();
            result = sendCommand_Cal(sendData, "Cal_ReadNum_String");
            if (result.first)
            {
                handleLogMessage_Adj("读取设备序列号成功: " + result.second);
                emit signal_readCommand_AdjDialog_Result(result.second, "readNum");
            }
            else
            {
                QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取序列号失败：" + result.second;
                handleLogMessage_Adj(errorMsg);
                QMessageBox::critical(verificationDialog, "错误", errorMsg);
            }
        }
        else
        {
            // 非透传模式设备，使用原有逻辑
            // 此处获取设备型号值用来区分ZCLOG 334设备
            if (deviceModel == "ZCLOG 334NTC")
            {
                // QByteArray sendData = QByteArray::fromHex("FEFEFEFE013C0003"); // 读取设备序列号
                // sendCommand_Cal(sendData, "Cal_ReadNum");
            }
            else
            {
                // TM24ND-P-S使用不同的序列号地址
                QString serialAddr = (deviceModel == "TM24ND-P-S") ? "00 10 00 05" : "00 11 00 05";
                QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + serialAddr);
                result = sendCommand_Cal(sendData, "Cal_ReadNum");

                if (result.first)
                {
                    handleLogMessage_Adj("读取设备序列号成功: " + result.second);
                    emit signal_readCommand_AdjDialog_Result(result.second, "readNum");
                }
                else
                {
                    QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取序列号失败：" + result.second;
                    handleLogMessage_Adj(errorMsg);
                    QMessageBox::critical(verificationDialog, "错误", errorMsg);
                }
            }
        }
    }
    else if (featureCode == "readBoardCardNumber")
    {
        // 检查1618A-L是否需要透传模式
        if (deviceModel == "1618A-L")
        {
            if (!checkTransparentModeRequirement(deviceModel, "读取板卡编号"))
            {
                return;
            }
        }

        handleLogMessage_Adj("开始读取板卡编号...");
        // 1618A-L和1618A-N使用不同的板卡编号地址
        QString boardCardAddr = (deviceModel == "1618A-L") ? "00 04 00 05" : "03 4C 00 05";
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + boardCardAddr);
        result = sendCommand_Cal(sendData, "Cal_ReadBoardCardNumber");
        if (result.first)
        {
            handleLogMessage_Adj("读取设备板卡编号成功: " + result.second);
            emit signal_readCommand_AdjDialog_Result(result.second, "readBoardCardNumber");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取设备板卡编号失败：" + result.second;
            handleLogMessage_Adj(errorMsg);
            QMessageBox::critical(verificationDialog, "错误", errorMsg);
        }
    }
    else if (featureCode == "readBoardCardModel")
    { // 读取板卡精度
        // 检查1618A-L是否需要透传模式
        if (deviceModel == "1618A-L")
        {
            if (!checkTransparentModeRequirement(deviceModel, "读取板卡精度"))
            {
                return;
            }
        }

        handleLogMessage_Adj("开始读取板卡精度...");
        // 1618A-L和1618A-N使用不同的板卡精度地址
        QString boardModelAddr = (deviceModel == "1618A-L") ? "00 02 00 01" : "03 4A 00 01";
        QByteArray sendData = createModbusCommand(m_globalHeadStr + "03" + boardModelAddr);
        result = sendCommand_Cal(sendData, "Cal_ReadBoardCardModel");
        if (result.first)
        {
            handleLogMessage_Adj("读取设备板卡精度成功: " + result.second);
            emit signal_readCommand_AdjDialog_Result(result.second, "readBoardCardModel");
        }
        else
        {
            QString errorMsg = result.second == "timeout" ? "操作超时！" : "读取板卡精度失败：" + result.second;
            handleLogMessage_Adj(errorMsg);
            QMessageBox::critical(verificationDialog, "错误", errorMsg);
        }
    }
}

void MainWindow::on_startAdjustment_clicked()
{
    verificationDialog->initVerificationDialog(ui->deviceModel_Cal->currentText());

    // verificationDialog->show(); // 使用 show() 显示非模态弹窗

    // 使用 exec() 以模态方式显示对话框
    verificationDialog->setWindowModality(Qt::ApplicationModal);
    verificationDialog->exec();
}

/*------------------------------------------------------------------*/
void MainWindow::initializeUI()
{
    setWindowTitle("测温模块自动校准软件_V" + QApplication::applicationVersion());

    // 调整StackedWidget的内边距，防止留白过大
    adjustStackedWidgetMargins(ui->addressStack_Cal);
    adjustStackedWidgetMargins(ui->rateStack_Cal);
    adjustStackedWidgetMargins(ui->addressStack_1220);
    adjustStackedWidgetMargins(ui->rateStack_1220);

    // "1611A-NTC","1611A-NTC-PLUS","1611A-RTD","1611A-RTD-PLUS"
    // "1611A-HT",
    // "TM22XND-P","TM18ND-P","TM18RD-P",
    // "ZCLOG 334","ZCLOG 611A"
    // "TM222ND-P","TM224ND-P","TM228ND-P"

    // "618A NTC-32",
    // "618A NTC-32-TIME" 特殊情况 单精度浮点数 需要额外修改batchxx批量标定校准的动态地址生成
    // "1611A-NTC" "1611A-RTD"这位更是史中史 双参考切换 需要唤醒
    // "1611A-HT" 这位也是史中史 分为1611A-HT(PT100)、1611A-HT(PT1000)两种 导出报告要求设备型号一致 要唤醒/关闭唤醒（读写序列号需要在唤醒之前） 需要单精度
    // "1618A" 更是一坨 设备套娃板卡
    // "619A NTC-32 PLUS" 多通道 双精度

    QStringList calDevices = {
        "618A",
        "618A PLUS",
        "618A PLUS(6NTC+2P-AP23)",
        "619A PLUS",
        "618A RTD",
        "618A RTD PLUS",
        "619A RTD PLUS",
        "618A NTC-32-TIME",
        "619A NTC-32 PLUS",
        "TM14RD-PT100",
        "TM14RD-PT1000",
        "TM14ND",
        "TM14ND-T",
        "TM14ND-P",
        "TM14ND-P-S",
        "TM24ND-P-S",
        "TM18ND-P",
        "TM18RD-P",
        "H-LCW-22B",
        "1618A-N",
        "1618A-L",
        "1611A-HT(PT100)",
        "1611A-HT(PT1000)",
    }; // "618A NTC-32", "618A NTC-32-TIME", "619A NTC-32 PLUS", "619A RTD-32 PLUS",
    ui->deviceModel_Cal->addItems(calDevices);
    // 为每个项目设置对应的tooltip
    for (int i = 0; i < ui->deviceModel_Cal->count(); i++)
    {
        ui->deviceModel_Cal->setItemData(i, ui->deviceModel_Cal->itemText(i), Qt::ToolTipRole);
    }

    // // 获取模型
    // QStandardItemModel* model = qobject_cast<QStandardItemModel*>(ui->deviceModel_Cal->model());

    // // 禁用item
    // QSet<QString> disabledItems = {"1611A-HT(PT100)","1611A-HT(PT1000)"};
    // for (int i = 0; i < model->rowCount(); ++i) {
    //     QStandardItem* item = model->item(i);
    //     if (item && disabledItems.contains(item->text())) {
    //         // 禁用该item，使其不可选且显示灰色
    //         Qt::ItemFlags flags = item->flags();
    //         flags &= ~Qt::ItemIsEnabled;
    //         flags &= ~Qt::ItemIsSelectable;
    //         item->setFlags(flags);
    //     }
    // }

    QStringList calDeviceNames = {"精密测温模块", "高精度多路测温模块", "精密测温仪", "温度采集器", "温度变送器", "测温及控制模块", "无线测温模块",
                                  "多通道温度巡检仪", "多通道温湿度巡检仪", "RTD 晶圆测温模块", "NTC 晶圆测温模块", "灭菌器温度验证仪",
                                  "1618A-NTC板卡", "1618A-RTD板卡", "1618A-TC板卡"};
    ui->deviceName_Cal->addItems(calDeviceNames);

    // 为每个项目设置对应的tooltip
    for (int i = 0; i < ui->deviceName_Cal->count(); i++)
    {
        ui->deviceName_Cal->setItemData(i, ui->deviceName_Cal->itemText(i), Qt::ToolTipRole);
    }

    // 初始化端口列表
    refreshSerial_Cal();

    // 设置波特率选项
    ui->baudNum_Cal->addItems(baudRates);
    ui->baudNum_Cal->setCurrentText("9600");
    ui->disconnectButton_Cal->setEnabled(false);

    QStringList communicationTypes = {"串口", "网络"};
    ui->communicationType_Cal->addItems(communicationTypes);
    ui->communicationType_Cal->setCurrentText("串口");

    ui->deviceModel_1220->addItem("1220");

    refreshSerial_1220();
    ui->baudNum_1220->addItems(baudRates);
    ui->baudNum_1220->setCurrentText("115200");
    ui->disconnectButton_1220->setEnabled(false);

    ui->communicationType_1220->addItems(communicationTypes);
    ui->communicationType_1220->setCurrentText("串口");

    // 初始化开关切换延迟时间选项
    QStringList delayOptions = {"10", "15", "20", "30", "40", "50", "60", "70", "80", "90"};
    if (ui->switchDelay)
    { // 检查UI组件是否存在
        ui->switchDelay->addItems(delayOptions);
        ui->switchDelay->setCurrentText("30"); // 默认选择30秒
    }

    ui->referGear1->addItems(referGears);
    ui->referGear2->addItems(referGears);
    ui->referGear3->addItems(referGears);
    ui->referGear4->addItems(referGears);
    ui->referGear5->addItems(referGears);

    QStringList referChannels1;
    for (int i = 1; i <= 5; ++i)
    { // 1-5通道设置为参考阻值通道
        referChannels1 << QString("CH%1").arg(i);
    }
    ui->referCH1->addItems(referChannels1);
    ui->referCH2->addItems(referChannels1);
    ui->referCH3->addItems(referChannels1);
    ui->referCH4->addItems(referChannels1);
    ui->referCH5->addItems(referChannels1);

    ui->referCH1->setCurrentIndex(0); // 对应“CH1”
    ui->referCH2->setCurrentIndex(1); // 对应“CH2”
    ui->referCH3->setCurrentIndex(2);
    ui->referCH4->setCurrentIndex(3);
    ui->referCH5->setCurrentIndex(4);

    QList<QComboBox *> comboBoxes = this->findChildren<QComboBox *>();
    for (QComboBox *box : comboBoxes)
    {
        box->setMaxVisibleItems(4);
    }

    updateChannelNums_Cal(ui->deviceModel_Cal->currentText());
}

void MainWindow::adjustStackedWidgetMargins(QStackedWidget *stackedWidget)
{
    for (int i = 0; i < stackedWidget->count(); ++i)
    {
        stackedWidget->widget(i)->layout()->setContentsMargins(0, 0, 0, 0);
    }
}

void MainWindow::onDeviceModelChanged(int index)
{
    static int previousIndex = -1;

    // 如果是初始化调用，直接执行
    if (previousIndex == -1)
    {
        previousIndex = index;
        updateChannelNums_Cal(ui->deviceModel_Cal->itemText(index));
        return;
    }

    // 检查透传模式状态，如果处于透传模式则提示用户
    if (!checkTransparentModeBeforeOperation("切换设备型号"))
    {
        // 用户选择不继续，恢复之前的选择
        ui->deviceModel_Cal->blockSignals(true);
        ui->deviceModel_Cal->setCurrentIndex(previousIndex);
        ui->deviceModel_Cal->blockSignals(false);
        return;
    }

    // 更新previousIndex并执行切换
    previousIndex = index;
    updateChannelNums_Cal(ui->deviceModel_Cal->itemText(index));

    // 重置校准对话框状态（如果已打开）
    // 只有在非1618A型号切换时才重置，1618A型号根据板卡精度决定校准模式
    if (calibrationDialog && calibrationDialog->isVisible())
    {
        QString newDeviceModel = ui->deviceModel_Cal->itemText(index);
        if (!newDeviceModel.contains("1618A"))
        {
            resetCalibrationDialogState();
        }
    }
}

void MainWindow::updateChannelNums_Cal(const QString &model)
{

    ui->chNums_Cal->clear();
    QStringList channels;

    QStringList supportedCH8_CH16Models = {"618A", "618A PLUS", "619A PLUS", "618A RTD", "618A RTD PLUS", "619A RTD PLUS"};
    if (supportedCH8_CH16Models.contains(model))
    {
        channels << "8" << "16";
        // isKOhm = true;
    }

    QStringList supportedCH6Models = {"618A PLUS(6NTC+2P-AP23)"};
    if (supportedCH6Models.contains(model))
    {
        channels << "6";
    }

    QStringList supportedCH4Models = {"TM14RD-PT100", "TM14RD-PT1000", "TM14ND", "TM14ND-T", "TM14ND-P", "TM14ND-P-S", "TM224ND-P", "TM24ND-P-S"};
    if (supportedCH4Models.contains(model))
    {
        channels << "4";
    }

    QStringList supportedCH12Models = {"H-LCW-22B"};
    if (supportedCH12Models.contains(model))
    {
        channels << "12";
    }

    // 1618A 并未预设选中参考电阻 因为分为NTC/RTD两种 需要手动选择
    QStringList supportedCH8Models = {"TM18ND-P", "TM18RD-P", "TM228ND-P", "1611A-NTC", "1618A-N", "1618A-L"};
    if (supportedCH8Models.contains(model))
    {
        channels << "8";
    }

    QStringList supportedCH2Models = {"TM222ND-P"};
    if (supportedCH2Models.contains(model))
    {
        channels << "2";
    }

    QStringList supportedCH32Models = {"618A NTC-32", "618A NTC-32-TIME", "619A NTC-32 PLUS", "619A RTD-32 PLUS"};
    if (supportedCH32Models.contains(model))
    {
        channels << "32";
    }

    QStringList supportedCH15Models = {"1611A-HT(PT100)", "1611A-HT(PT1000)"};
    if (supportedCH15Models.contains(model))
    {
        channels << "15";
    }

    for (const QString &channel : channels)
    {
        ui->chNums_Cal->addItem(channel);
    }

    // 预设 referGear1, referGear2, referGear3 的值
    // QStringList referGears = {"0Ω", "10Ω", "100Ω", "200Ω", "350Ω", "1kΩ", "10kΩ", "20kΩ"};
    QMap<QString, QStringList> modelToGears = {
        {"618A", {"1kΩ", "10kΩ", "20kΩ"}},
        {"618A PLUS", {"1kΩ", "10kΩ", "20kΩ"}},
        {"618A PLUS(6NTC+2P-AP23)", {"1kΩ", "10kΩ", "20kΩ"}},
        {"619A PLUS", {"1kΩ", "10kΩ", "20kΩ"}},
        {"618A RTD", {"100Ω", "200Ω", "350Ω"}},
        {"618A RTD PLUS", {"100Ω", "200Ω", "350Ω"}},
        {"619A RTD PLUS", {"100Ω", "200Ω", "350Ω"}},

        {"TM14ND", {"1kΩ", "10kΩ", "20kΩ"}},
        {"TM14ND-T", {"1kΩ", "10kΩ", "20kΩ"}},
        {"TM14ND-P", {"1kΩ", "10kΩ", "20kΩ"}},
        {"TM14ND-P-S", {"1kΩ", "10kΩ", "20kΩ"}},

        {"H-LCW-22B", {"1kΩ", "10kΩ", "20kΩ"}},

        {"TM18ND-P", {"1kΩ", "10kΩ", "20kΩ"}},
        {"TM18RD-P", {"350Ω", "1kΩ", "2kΩ"}},
        {"TM24ND-P-S", {"1kΩ", "10kΩ", "20kΩ"}},
        {"TM222ND-P", {"1kΩ", "10kΩ", "20kΩ"}},
        {"TM224ND-P", {"1kΩ", "10kΩ", "20kΩ"}},
        {"TM228ND-P", {"1kΩ", "10kΩ", "20kΩ"}},

        {"618A NTC-32", {"1kΩ", "10kΩ", "20kΩ"}},

        {"619A NTC-32 PLUS", {"1kΩ", "10kΩ", "20kΩ"}},
        {"619A RTD-32 PLUS", {"100Ω", "200Ω", "350Ω"}},

        {"1611A-NTC", {"1kΩ", "10kΩ", "20kΩ"}},

        {"1611A-HT(PT100)", {"100Ω", "200Ω", "350Ω"}},
        {"1611A-HT(PT1000)", {"350Ω", "1kΩ", "2kΩ"}},

        {"TM14RD-PT1000", {"350Ω", "1kΩ", "2kΩ"}},

    };

    QMap<QString, QStringList> modelTo4Gears = {
        {"618A NTC-32-TIME", {"1kΩ", "5kΩ", "10kΩ", "20kΩ"}},
    };

    QMap<QString, QStringList> modelTo2Gears = {
        {"TM14RD-PT100", {"100Ω", "200Ω"}},
    };

    // Models that use all 5 gears
    QMap<QString, QStringList> modelToFiveGears = {
        {"1611A-RTD", {"100Ω", "200Ω", "350Ω", "1kΩ", "2kΩ"}},
        {"1611A-RTD PLUS", {"100Ω", "200Ω", "350Ω", "1kΩ", "2kΩ"}},
    };

    // 首先重置所有referGear为"0"，并启用所有复选框
    setComboBoxValue(ui->referGear1, referGears, "0");
    setComboBoxValue(ui->referGear2, referGears, "0");
    setComboBoxValue(ui->referGear3, referGears, "0");
    setComboBoxValue(ui->referGear4, referGears, "0");
    setComboBoxValue(ui->referGear5, referGears, "0");

    ui->refer1->setEnabled(true);
    ui->refer1->setChecked(false);
    ui->refer2->setEnabled(true);
    ui->refer2->setChecked(false);
    ui->refer3->setEnabled(true);
    ui->refer3->setChecked(false);
    ui->refer4->setEnabled(true);
    ui->refer4->setChecked(false);
    ui->refer5->setEnabled(true);
    ui->refer5->setChecked(false);

    if (modelToFiveGears.contains(model))
    {
        // Handle models with 5 gears - 使用全部5个，不禁用任何复选框
        QStringList gears = modelToFiveGears[model];
        setComboBoxValue(ui->referGear1, referGears, gears[0]);
        setComboBoxValue(ui->referGear2, referGears, gears[1]);
        setComboBoxValue(ui->referGear3, referGears, gears[2]);
        setComboBoxValue(ui->referGear4, referGears, gears[3]);
        setComboBoxValue(ui->referGear5, referGears, gears[4]);
    }
    else if (modelTo4Gears.contains(model))
    {
        // Handle models with 4 gears - 禁用refer5
        QStringList gears = modelTo4Gears[model];
        setComboBoxValue(ui->referGear1, referGears, gears[0]);
        setComboBoxValue(ui->referGear2, referGears, gears[1]);
        setComboBoxValue(ui->referGear3, referGears, gears[2]);
        setComboBoxValue(ui->referGear4, referGears, gears[3]);
        // referGear5 保持为"0"
        ui->refer5->setEnabled(false);
    }
    else if (modelTo2Gears.contains(model))
    {
        // Handle models with 2 gears - 禁用refer3, refer4, refer5
        QStringList gears = modelTo2Gears[model];
        setComboBoxValue(ui->referGear1, referGears, gears[0]);
        setComboBoxValue(ui->referGear2, referGears, gears[1]);
        // referGear3, referGear4, referGear5 保持为"0"
        ui->refer3->setEnabled(false);
        ui->refer4->setEnabled(false);
        ui->refer5->setEnabled(false);
    }
    else if (modelToGears.contains(model))
    {
        // Handle models with 3 gears - 禁用refer4, refer5
        QStringList gears = modelToGears[model];
        setComboBoxValue(ui->referGear1, referGears, gears[0]);
        setComboBoxValue(ui->referGear2, referGears, gears[1]);
        setComboBoxValue(ui->referGear3, referGears, gears[2]);
        // referGear4, referGear5 保持为"0"
        ui->refer4->setEnabled(false);
        ui->refer5->setEnabled(false);
    }
}

void MainWindow::setComboBoxValue(QComboBox *comboBox, const QStringList &items, const QString &value)
{
    comboBox->clear();
    comboBox->addItems(items);
    int index = comboBox->findText(value);
    if (index != -1)
    {
        comboBox->setCurrentIndex(index);
    }
}

void MainWindow::onCommunicationType_CalChanged(int index)
{
    // 检查透传模式状态
    if (!checkTransparentModeBeforeOperation("切换通信类型"))
    {
        // 用户选择不继续，恢复之前的选择
        ui->communicationType_Cal->blockSignals(true);
        ui->communicationType_Cal->setCurrentIndex(index == 0 ? 1 : 0);
        ui->communicationType_Cal->blockSignals(false);
        return;
    }

    // 检查当前连接状态
    bool isCurrentlyConnected = false;
    if (index == 0)
    { // 切换到串口模式
        if (m_calDeviceNetworkHandler && m_calDeviceNetworkHandler->isConnected())
        {
            // 当前网络正在连接中
            QMessageBox::StandardButton reply = QMessageBox::question(this,
                                                                      "切换确认",
                                                                      "当前网络连接正在使用中，切换将断开连接。是否继续？",
                                                                      QMessageBox::Yes | QMessageBox::No);

            if (reply == QMessageBox::No)
            {
                // 用户取消切换，恢复combobox选择
                ui->communicationType_Cal->blockSignals(true);
                ui->communicationType_Cal->setCurrentIndex(1);
                ui->communicationType_Cal->blockSignals(false);
                return;
            }
            // 断开网络连接
            m_calDeviceNetworkHandler->disconnectFromHost();
            isCurrentlyConnected = true;
        }
    }
    else
    { // 切换到网络模式
        if (m_calDeviceSerialHandler && m_calDeviceSerialHandler->isPortOpen())
        {
            // 当前串口正在连接中
            QMessageBox::StandardButton reply = QMessageBox::question(this,
                                                                      "切换确认",
                                                                      "当前串口正在使用中，切换将关闭串口。是否继续？",
                                                                      QMessageBox::Yes | QMessageBox::No);

            if (reply == QMessageBox::No)
            {
                // 用户取消切换，恢复combobox选择
                ui->communicationType_Cal->blockSignals(true);
                ui->communicationType_Cal->setCurrentIndex(0);
                ui->communicationType_Cal->blockSignals(false);
                return;
            }
            // 关闭串口
            m_calDeviceSerialHandler->closePort();
            isCurrentlyConnected = true;
        }
    }

    // 切换堆栈窗口页面
    ui->addressStack_Cal->setCurrentIndex(index);
    ui->rateStack_Cal->setCurrentIndex(index);

    // 更新标签文本
    if (index == 0)
    { // 串口模式
        ui->addressLabel_Cal->setText("串口号：");
        ui->rateLabel_Cal->setText("波特率：");
        refreshSerial_Cal();
    }
    else
    { // 网络模式
        ui->addressLabel_Cal->setText("IP地址：");
        ui->rateLabel_Cal->setText("端口号：");
    }

    // 重置连接相关按钮状态
    ui->connectButton_Cal->setEnabled(true);
    ui->disconnectButton_Cal->setEnabled(false);

    // 如果之前有连接，提示用户重新连接
    if (isCurrentlyConnected)
    {
        QMessageBox::information(this, "提示", "请重新建立连接");
    }
}

void MainWindow::onCommunicationType_1220Changed(int index)
{
    // 检查当前连接状态
    bool isCurrentlyConnected = false;
    if (index == 0)
    { // 切换到串口模式
        if (m_1220DeviceNetworkHandler && m_1220DeviceNetworkHandler->isConnected())
        {
            // 当前网络正在连接中
            QMessageBox::StandardButton reply = QMessageBox::question(this,
                                                                      "切换确认",
                                                                      "当前网络连接正在使用中，切换将断开连接。是否继续？",
                                                                      QMessageBox::Yes | QMessageBox::No);

            if (reply == QMessageBox::No)
            {
                // 用户取消切换，恢复combobox选择
                ui->communicationType_1220->blockSignals(true);
                ui->communicationType_1220->setCurrentIndex(1);
                ui->communicationType_1220->blockSignals(false);
                return;
            }
            // 断开网络连接
            m_1220DeviceNetworkHandler->disconnectFromHost();
            isCurrentlyConnected = true;
        }
    }
    else
    { // 切换到网络模式
        if (m_1220DeviceSerialHandler && m_1220DeviceSerialHandler->isPortOpen())
        {
            // 当前串口正在连接中
            QMessageBox::StandardButton reply = QMessageBox::question(this,
                                                                      "切换确认",
                                                                      "当前串口正在使用中，切换将关闭串口。是否继续？",
                                                                      QMessageBox::Yes | QMessageBox::No);

            if (reply == QMessageBox::No)
            {
                // 用户取消切换，恢复combobox选择
                ui->communicationType_1220->blockSignals(true);
                ui->communicationType_1220->setCurrentIndex(0);
                ui->communicationType_1220->blockSignals(false);
                return;
            }
            // 关闭串口
            m_1220DeviceSerialHandler->closePort();
            isCurrentlyConnected = true;
        }
    }

    // 切换堆栈窗口页面
    ui->addressStack_1220->setCurrentIndex(index);
    ui->rateStack_1220->setCurrentIndex(index);

    // 更新标签文本
    if (index == 0)
    { // 串口模式
        ui->addressLabel_1220->setText("串口号：");
        ui->rateLabel_1220->setText("波特率：");
        refreshSerial_1220();
    }
    else
    { // 网络模式
        ui->addressLabel_1220->setText("IP地址：");
        ui->rateLabel_1220->setText("端口号：");
    }

    // 重置连接相关按钮状态
    ui->connectButton_1220->setEnabled(true);
    ui->disconnectButton_1220->setEnabled(false);

    // 如果之前有连接，提示用户重新连接
    if (isCurrentlyConnected)
    {
        QMessageBox::information(this, "提示", "请重新建立连接");
    }
}

void MainWindow::refreshSerial_Cal()
{
    const auto infos = QSerialPortInfo::availablePorts();
    ui->portNum_Cal->clear();
    for (const QSerialPortInfo &info : infos)
    {
        ui->portNum_Cal->addItem(info.portName());
    }
}

void MainWindow::refreshSerial_1220()
{
    const auto infos = QSerialPortInfo::availablePorts();
    ui->portNum_1220->clear();
    for (const QSerialPortInfo &info : infos)
    {
        ui->portNum_1220->addItem(info.portName());
    }
}

bool MainWindow::checkConnection()
{
    if (ui->communicationType_Cal->currentIndex() == 0)
    {
        if (!m_calDeviceSerialHandler->isPortOpen())
        {
            QMessageBox::warning(this, "提示", "校准设备串口未连接！");
            return false;
        }
    }
    else
    {
        if (!m_calDeviceNetworkHandler->isConnected())
        {
            QMessageBox::warning(this, "提示", "校准设备网络未连接！");
            return false;
        }
    }

    if (ui->communicationType_1220->currentIndex() == 0)
    {
        if (!m_1220DeviceSerialHandler->isPortOpen())
        {
            QMessageBox::warning(this, "提示", "1220设备串口未连接！");
            return false;
        }
    }
    else
    {
        if (!m_1220DeviceNetworkHandler->isConnected())
        {
            QMessageBox::warning(this, "提示", "1220设备网络未连接！");
            return false;
        }
    }

    return true;
}

QByteArray MainWindow::createModbusCommand(const QString &hexCommand)
{
    // 移除所有空格
    QString cleanCommand = hexCommand.simplified().remove(' ');

    // 将十六进制字符串转换为 QByteArray
    QByteArray cmd = QByteArray::fromHex(cleanCommand.toLatin1());

    // 计算并添加 CRC
    uint16_t crc = calculateCRC16(cmd);
    cmd.append(static_cast<char>(crc & 0xFF));
    cmd.append(static_cast<char>((crc >> 8) & 0xFF));

    return cmd;
}

uint16_t MainWindow::calculateCRC16(const QByteArray &data)
{
    uint16_t crc = 0xFFFF;
    for (char ch : data)
    {
        crc ^= static_cast<uint8_t>(ch);
        for (int i = 0; i < 8; ++i)
        {
            if (crc & 0x0001)
            {
                crc = (crc >> 1) ^ 0xA001;
            }
            else
            {
                crc = crc >> 1;
            }
        }
    }
    return crc;
}

// 辅助函数：根据设备类型格式化单个寄存器的字节序
// 注意：此函数仅用于单个寄存器的06功能码操作（波特率、滤波次数等）
// 多个寄存器的10功能码操作（参考阻值写入）不使用此函数
QString MainWindow::formatValueForDevice(const QString &deviceModel, uint16_t value)
{
    if (deviceModel == "TM24ND-P-S")
    {
        // TM24ND-P-S的单个寄存器操作使用大端模式：高字节在前，低字节在后
        uint8_t highByte = (value >> 8) & 0xFF;
        uint8_t lowByte = value & 0xFF;
        return QString("%1 %2").arg(highByte, 2, 16, QChar('0')).arg(lowByte, 2, 16, QChar('0')).toUpper();
    }
    else
    {
        // 其他设备的单个寄存器操作使用小端模式：低字节在前，高字节在后
        uint8_t highByte = (value >> 8) & 0xFF;
        uint8_t lowByte = value & 0xFF;
        return QString("%1 %2").arg(lowByte, 2, 16, QChar('0')).arg(highByte, 2, 16, QChar('0')).toUpper();
    }
}

/*
// 01  00 59  从哪个通道读取(0为CH1)  读取数量(16)
QByteArray MainWindow::createModbusReadFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, int numChannels)
{
    // 计算起始寄存器地址
    uint16_t addr = firstAddr + channel * 4;
    uint8_t addr_high = (addr >> 8) & 0xFF;  // 提取高字节
    uint8_t addr_low = addr & 0xFF;          // 提取低字节

    // 计算需要读取的寄存器数量
    uint16_t numRegisters = numChannels * 4;
    uint8_t numRegisters_high = (numRegisters >> 8) & 0xFF;  // 提取高字节
    uint8_t numRegisters_low = numRegisters & 0xFF;          // 提取低字节

    // 构造 Modbus RTU 请求帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr));        // 设备地址
    frame.append(static_cast<char>(0x03));              // 功能码 03（读取保持寄存器）
    frame.append(static_cast<char>(addr_high));         // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));          // 寄存器地址低字节
    frame.append(static_cast<char>(numRegisters_high)); // 寄存器数量高字节
    frame.append(static_cast<char>(numRegisters_low));  // 寄存器数量低字节

    return frame;
}

// 写入单个通道参考阻值（Modbus功能码10） channel为0时代表第一个通道 channel用于计算寄存器首地址
QByteArray MainWindow::createModbusWriteFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, double ref_value)
{
    // 计算该通道的寄存器地址（每个通道占用 4 个寄存器）
    uint16_t addr = firstAddr + channel * 4;
    uint8_t addr_high = (addr >> 8) & 0xFF; // 高字节
    uint8_t addr_low = addr & 0xFF;         // 低字节

    // 将 double 类型转换为小端字节序的 8 字节数据
    QByteArray byteArray;
    QDataStream stream(&byteArray, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);
    stream << ref_value;

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr));        // 设备地址（示例为 01，可配置）
    frame.append(static_cast<char>(0x10));        // 功能码 10（写多个寄存器）
    frame.append(static_cast<char>(addr_high));   // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));    // 寄存器地址低字节
    frame.append(static_cast<char>(0x00));        // 寄存器数量高字节
    frame.append(static_cast<char>(0x04));        // 寄存器数量低字节（4 个寄存器）
    frame.append(static_cast<char>(0x08));        // 字节数（8 字节）
    frame.append(byteArray);                      // 数据（8 字节）

    // QByteArray sendData = createModbusCommand(QString::fromLatin1(frame.toHex()));

    return frame;
}

// referChannel---要打开的参考电阻所在通道  calChannel--要打开的校准设备所在通道
QByteArray MainWindow::createModbusOpen1220Frame(int referChannel, int calChannel)
{
    // 创建一个4字节的数组，初始化为0
    QByteArray byteArray(4, 0);

    // 设置referChannel对应的位
    if (referChannel >= 1 && referChannel <= 20) {
        int byteIndex = (referChannel - 1) / 8;     // 确定在哪个字节
        int bitPosition = (referChannel - 1) % 8;    // 确定在字节中的位置
        char byte = byteArray[byteIndex];           // 获取当前字节
        byte |= (0x01 << bitPosition);             // 设置位
        byteArray[byteIndex] = byte;               // 将修改后的字节写回数组
    }

    // 设置calChannel对应的位
    if (calChannel >= 1 && calChannel <= 20) {
        int byteIndex = (calChannel - 1) / 8;
        int bitPosition = (calChannel - 1) % 8;
        char byte = byteArray[byteIndex];
        byte |= (0x01 << bitPosition);
        byteArray[byteIndex] = byte;
    }

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(0x01));    // 设备地址
    frame.append(static_cast<char>(0x10));    // 功能码 10
    frame.append(static_cast<char>(0x00));    // 寄存器地址高字节
    frame.append(static_cast<char>(0x33));    // 寄存器地址低字节
    frame.append(static_cast<char>(0x00));    // 寄存器数量高字节
    frame.append(static_cast<char>(0x02));    // 寄存器数量低字节
    frame.append(static_cast<char>(0x04));    // 字节数
    frame.append(byteArray);                  // 添加生成的4字节数据

    return frame;
}
*/

// 电压校准信号处理函数
void MainWindow::on_signal_startVoltageCal_CalDialog(const QString &deviceModel, const QString &featureCode)
{
    if (featureCode == "startVoltageCal")
    {
        handleLogMessage("开始电压校准...");

        // 创建设备配置
        CalDeviceConfig device;
        device.name = deviceModel;
        device.num_channels = 1; // TC类型只校准通道1

        // 设置设备配置到电压校准Worker
        voltageCalibrationWorker->setDeviceConfig(device);

        // 禁用相关按钮
        if (calibrationDialog->getUi()->startCal_CalDialog_2)
        {
            calibrationDialog->getUi()->startCal_CalDialog_2->setEnabled(false);
        }
        if (calibrationDialog->getUi()->abortButton_2)
        {
            calibrationDialog->getUi()->abortButton_2->setEnabled(true);
        }

        // 初始化电压校准表格
        initializeVoltageCalibrationTable();

        // 启动电压校准
        emit startVoltageCalibration();
    }
}

// 电压校准完成处理
void MainWindow::handleVoltageCalibrationFinished(bool success)
{
    // 添加校准结果汇总行
    addVoltageCalibrationSummary(success);

    // 恢复按钮状态
    if (calibrationDialog->getUi()->startCal_CalDialog_2)
    {
        calibrationDialog->getUi()->startCal_CalDialog_2->setEnabled(true);
    }
    if (calibrationDialog->getUi()->abortButton_2)
    {
        calibrationDialog->getUi()->abortButton_2->setEnabled(false);
    }

    // 重置进度条为100%
    if (calibrationDialog->getUi()->progressBar_2)
    {
        calibrationDialog->getUi()->progressBar_2->setValue(100);
    }

    if (success)
    {
        QMessageBox::information(calibrationDialog, "成功", "TC类型电压校准成功完成！\n\n0mV和50mV两个档位均校准合格。");
    }
    else
    {
        QMessageBox::warning(calibrationDialog, "失败", "TC类型电压校准失败！\n\n请检查设备连接、754设备输出设置，或查看详细日志信息。");
    }
}

// 电压校准日志处理
void MainWindow::handleVoltageLogMessage(const QString &message)
{
    handleLogMessage(message);
}

// 电压校准数据更新处理 - 每次读取都更新表格数据
void MainWindow::handleUpdateVoltageData(double voltage, double deviation, bool passed)
{
    if (!calibrationDialog->getUi()->readDataTable_2)
        return;

    QTableWidget *table = calibrationDialog->getUi()->readDataTable_2;

    // 根据VoltageCalibrationWorker的当前档位确定更新哪一行
    int targetRow = -1;
    double referenceVoltage = 0.0;

    if (voltageCalibrationWorker && voltageCalibrationWorker->getCurrentLevel() == 0)
    {
        // 0mV档位，更新第0行
        targetRow = 0;
        referenceVoltage = 0.0;
    }
    else if (voltageCalibrationWorker && voltageCalibrationWorker->getCurrentLevel() == 1)
    {
        // 50mV档位，更新第1行
        targetRow = 1;
        referenceVoltage = 50.0;
    }
    else
    {
        qDebug() << "无法确定当前电压校准档位";
        return;
    }

    // 更新测量电压
    QTableWidgetItem *voltageItem = table->item(targetRow, 2);
    if (!voltageItem)
    {
        voltageItem = new QTableWidgetItem();
        voltageItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(targetRow, 2, voltageItem);
    }
    voltageItem->setText(QString::number(voltage, 'f', 6));

    // 更新电压偏差（带正负号）
    QTableWidgetItem *deviationItem = table->item(targetRow, 3);
    if (!deviationItem)
    {
        deviationItem = new QTableWidgetItem();
        deviationItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(targetRow, 3, deviationItem);
    }
    double signedDeviation = voltage - referenceVoltage; // 带符号的偏差
    QString deviationText = QString("%1%2").arg(signedDeviation >= 0 ? "+" : "").arg(signedDeviation, 0, 'f', 6);
    deviationItem->setText(deviationText);
    deviationItem->setForeground(passed ? Qt::green : Qt::red);

    // 更新允许偏差
    QTableWidgetItem *allowedDeviationItem = table->item(targetRow, 4);
    if (!allowedDeviationItem)
    {
        allowedDeviationItem = new QTableWidgetItem();
        allowedDeviationItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(targetRow, 4, allowedDeviationItem);
    }
    allowedDeviationItem->setText("±0.010");

    // 校准结果暂时不更新，等档位完成后再更新
    QTableWidgetItem *resultItem = table->item(targetRow, 5);
    if (!resultItem)
    {
        resultItem = new QTableWidgetItem();
        resultItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(targetRow, 5, resultItem);
    }
    // 如果还没有结果，显示"校准中..."
    if (resultItem->text().isEmpty() || resultItem->text() == "校准中...")
    {
        resultItem->setText("校准中...");
        resultItem->setForeground(Qt::blue);
    }
}

// 档位校准完成时更新校准结果
void MainWindow::handleVoltageLevelCompleted(int level, double finalVoltage, double finalDeviation, bool levelPassed)
{
    if (!calibrationDialog->getUi()->readDataTable_2)
        return;

    QTableWidget *table = calibrationDialog->getUi()->readDataTable_2;

    // 确定更新哪一行
    int targetRow = level; // 0mV档位更新第0行，50mV档位更新第1行

    if (targetRow < 0 || targetRow >= table->rowCount())
    {
        qDebug() << "无效的档位索引:" << level;
        return;
    }

    // 只更新校准结果（基于最后三次是否都合格）
    QTableWidgetItem *resultItem = table->item(targetRow, 5);
    if (!resultItem)
    {
        resultItem = new QTableWidgetItem();
        resultItem->setTextAlignment(Qt::AlignCenter);
        table->setItem(targetRow, 5, resultItem);
    }
    resultItem->setText(levelPassed ? "合格" : "不合格");
    resultItem->setForeground(levelPassed ? Qt::green : Qt::red);

    qDebug() << QString("档位%1校准完成: %2").arg(level).arg(levelPassed ? "合格" : "不合格");
}

// 处理电压校准用户提示
void MainWindow::handleVoltageUserPrompt(const QString &message)
{
    // 在主线程中创建并显示对话框
    QMessageBox msgBox(calibrationDialog);
    msgBox.setWindowTitle("电压校准提示");
    msgBox.setText(message);
    msgBox.setStandardButtons(QMessageBox::Ok | QMessageBox::Cancel);
    msgBox.setButtonText(QMessageBox::Ok, "确定");
    msgBox.setButtonText(QMessageBox::Cancel, "取消");
    msgBox.setDefaultButton(QMessageBox::Ok);

    int result = msgBox.exec();
    bool confirmed = (result == QMessageBox::Ok);

    // 将结果发送回工作线程
    if (voltageCalibrationWorker)
    {
        QMetaObject::invokeMethod(voltageCalibrationWorker, "onUserPromptResult",
                                  Qt::QueuedConnection, Q_ARG(bool, confirmed));
    }
}

// 处理电压校准进度更新
void MainWindow::handleVoltageCalibrationProgress(int currentProgress, int totalProgress)
{
    if (calibrationDialog && calibrationDialog->getUi()->progressBar_2)
    {
        // 直接使用传入的进度值（0-100）
        calibrationDialog->getUi()->progressBar_2->setValue(currentProgress);

        QString statusText;
        if (currentProgress == 0)
        {
            statusText = "开始电压校准...";
        }
        else if (currentProgress == 50)
        {
            statusText = "0mV档位校准完成，进度: 50%";
        }
        else if (currentProgress == 100)
        {
            statusText = "50mV档位校准完成，进度: 100%";
        }
        else
        {
            statusText = QString("电压校准进度: %1%").arg(currentProgress);
        }

        handleLogMessage(statusText);
    }
}

// 中止电压校准
void MainWindow::abortVoltageCalibration()
{
    if (voltageCalibrationWorker)
    {
        voltageCalibrationWorker->requestAbort();
    }

    // 立即重置进度条和按钮状态
    if (calibrationDialog)
    {
        if (calibrationDialog->getUi()->progressBar_2)
        {
            calibrationDialog->getUi()->progressBar_2->setValue(0);
        }
        if (calibrationDialog->getUi()->startCal_CalDialog_2)
        {
            calibrationDialog->getUi()->startCal_CalDialog_2->setEnabled(true);
        }
        if (calibrationDialog->getUi()->abortButton_2)
        {
            calibrationDialog->getUi()->abortButton_2->setEnabled(false);
        }
    }
}

// 1618A TC电压校准相关处理方法
void MainWindow::handleVoltageUserPrompt_Adj(const QString &message)
{
    // 在主线程中创建并显示对话框
    QMessageBox msgBox(verificationDialog);
    msgBox.setWindowTitle("1618A TC电压校准提示");
    msgBox.setText(message);
    msgBox.setStandardButtons(QMessageBox::Ok | QMessageBox::Cancel);
    msgBox.setButtonText(QMessageBox::Ok, "确定");
    msgBox.setButtonText(QMessageBox::Cancel, "取消");
    msgBox.setDefaultButton(QMessageBox::Ok);

    int result = msgBox.exec();
    bool confirmed = (result == QMessageBox::Ok);

    // 将结果发送回工作线程
    if (********************************)
    {
        QMetaObject::invokeMethod(********************************, "onUserPromptResult",
                                  Qt::QueuedConnection, Q_ARG(bool, confirmed));
    }
}

void MainWindow::handleVoltageCalibrationFinished_Adj(bool success)
{
    QString result = success ? "1618A TC电压校准成功完成" : "1618A TC电压校准失败";
    handleLogMessage_Adj(result);

    // 重置按钮状态
    if (verificationDialog)
    {
        if (verificationDialog->getUi()->startCal_Adj_2)
        {
            verificationDialog->getUi()->startCal_Adj_2->setEnabled(true);
        }
        if (verificationDialog->getUi()->abortCal_Adj_2)
        {
            verificationDialog->getUi()->abortCal_Adj_2->setEnabled(false);
        }
        if (verificationDialog->getUi()->restartCal_Adj_2)
        {
            verificationDialog->getUi()->restartCal_Adj_2->setEnabled(true);
        }
        if (verificationDialog->getUi()->endCal_Adj_2)
        {
            verificationDialog->getUi()->endCal_Adj_2->setEnabled(true);
        }
        if (verificationDialog->getUi()->progressBar_Adj_2)
        {
            verificationDialog->getUi()->progressBar_Adj_2->setValue(success ? 100 : 0);
        }
    }
}

void MainWindow::handleVoltageCalibrationProgress_Adj(int currentStep, int totalSteps)
{
    if (verificationDialog && verificationDialog->getUi()->progressBar_Adj_2)
    {
        verificationDialog->getUi()->progressBar_Adj_2->setValue(currentStep);

        QString statusText = QString("1618A TC电压校准进度: %1%").arg(currentStep);
        handleLogMessage_Adj(statusText);
    }
}

// 处理VerificationDialog电压校准信号的槽函数实现
void MainWindow::on_signal_startVoltageCal_AdjDialog(const QString &deviceModel, const QString &featureCode)
{
    if (featureCode == "startVoltageCal")
    {
        handleLogMessage_Adj("开始1618A TC电压校准...");

        // 检查是否为1618A TC设备
        if (!(deviceModel.contains("1618A-N") || deviceModel.contains("1618A-L")))
        {
            handleLogMessage_Adj("错误：电压校准仅支持1618A TC设备");
            return;
        }

        // 创建设备配置
        CalDeviceConfig device;
        device.name = deviceModel;
        device.num_channels = 8; // CH1-CH8

        // 设置设备配置到1618A TC电压校准Worker
        ********************************->setDeviceConfig(device);

        // 禁用相关按钮
        if (verificationDialog->getUi()->startCal_Adj_2)
        {
            verificationDialog->getUi()->startCal_Adj_2->setEnabled(false);
        }
        if (verificationDialog->getUi()->abortCal_Adj_2)
        {
            verificationDialog->getUi()->abortCal_Adj_2->setEnabled(true);
        }
        if (verificationDialog->getUi()->restartCal_Adj_2)
        {
            verificationDialog->getUi()->restartCal_Adj_2->setEnabled(false);
        }
        if (verificationDialog->getUi()->endCal_Adj_2)
        {
            verificationDialog->getUi()->endCal_Adj_2->setEnabled(false);
        }

        // 生成电压校准表格
        generateVoltageAdjTable();

        // 启动1618A TC电压校准
        emit startTC1618AVoltageCalibration();
    }
}

void MainWindow::on_signal_abortVoltageCal_AdjDialog()
{
    handleLogMessage_Adj("用户中止1618A TC电压校准");
    emit abortTC1618AVoltageCalibration();
}

void MainWindow::on_signal_restartVoltageCal_AdjDialog(int channel, int level)
{
    handleLogMessage_Adj(QString("开始复校CH%1通道的%2档位...")
                             .arg(channel + 1)
                             .arg(level == 0 ? "-10mV" : level == 1 ? "10mV"
                                                     : level == 2   ? "30mV"
                                                     : level == 3   ? "50mV"
                                                                    : "75mV"));

    // 禁用相关按钮
    if (verificationDialog->getUi()->startCal_Adj_2)
    {
        verificationDialog->getUi()->startCal_Adj_2->setEnabled(false);
    }
    if (verificationDialog->getUi()->abortCal_Adj_2)
    {
        verificationDialog->getUi()->abortCal_Adj_2->setEnabled(true);
    }
    if (verificationDialog->getUi()->restartCal_Adj_2)
    {
        verificationDialog->getUi()->restartCal_Adj_2->setEnabled(false);
    }

    emit restartTC1618AVoltageCalibration(channel, level);
}

void MainWindow::on_signal_saveVoltageResults_AdjDialog()
{
    handleLogMessage_Adj("保存1618A TC电压校准结果...");

    if (validateVoltageTableData())
    {
        ProjectData projectData = extractVoltageProjectDataFromTable();
        if (MainWindow::saveProjectToDatabase(projectData))
        {
            loadCalibrationHistory_Adj();
            QMessageBox::information(verificationDialog, "成功", "1618A TC电压校准数据已保存");
        }
        else
        {
            QMessageBox::warning(verificationDialog, "错误", "保存1618A TC电压校准数据失败");
        }
    }
    else
    {
        QMessageBox::warning(verificationDialog, "警告", "电压校准表格数据无效，无法保存");
    }
}
