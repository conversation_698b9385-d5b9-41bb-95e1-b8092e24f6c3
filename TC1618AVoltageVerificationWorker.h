#ifndef TC1618AVOLTAGEVERIFICATIONWORKER_H
#define TC1618AVOLTAGEVERIFICATIONWORKER_H

#include <QObject>
#include <QTimer>
#include <QVector>
#include <QPair>
#include <QByteArray>
#include <QString>
#include <functional>
#include "CalDeviceConfig.h"

// 命令处理函数类型定义
using CommandHandlerFunc = std::function<QPair<bool, QString>(const QByteArray &, const QString &)>;

class TC1618AVoltageVerificationWorker : public QObject
{
    Q_OBJECT

public:
    explicit TC1618AVoltageVerificationWorker(QObject *parent = nullptr);
    ~TC1618AVoltageVerificationWorker();

    void setDeviceConfig(const CalDeviceConfig &config);
    void setCommandHandlers(CommandHandlerFunc calHandler);

    // 开始电压校准
    void startVoltageCalibration();
    
    // 开始单个项目的复校
    void startSingleItemRecalibration(int channel, int level);
    
    // 中止校准
    void requestAbort();
    
    // 用户提示结果处理
    void onUserPromptResult(bool confirmed);

signals:
    void logMessage(const QString &message);
    void voltageCalibrationFinished(bool success);
    void voltageCalibrationProgress(int currentStep, int totalSteps);
    void updateVoltageTableData(int channel, int level, const QVector<double> &voltageReadings, 
                               double avgVoltage, double deviation, double tolerance, bool passed);
    void requestUserPrompt(const QString &message);
    void clearVoltageTable();

private slots:
    void collectVoltageData();

private:
    struct VoltageLevel
    {
        double targetVoltage; // 目标电压值 (mV)
        QString description;  // 描述文本
    };

    CalDeviceConfig m_deviceConfig;
    bool m_abortRequested;
    QTimer *m_dataCollectionTimer;

    // 命令处理函数指针
    CommandHandlerFunc m_calCommandHandler;

    // 校准状态
    int m_currentChannel;              // 当前校准通道 (0-7对应CH1-CH8)
    int m_currentLevel;                // 当前校准档位 (0-4对应五个电压档位)
    int m_currentRound;                // 当前读取轮次
    QVector<double> m_voltageReadings; // 电压读取值
    
    // 复校模式标志
    bool m_isRecalibrationMode;
    int m_recalibrationChannel;
    int m_recalibrationLevel;

    static const QVector<VoltageLevel> VOLTAGE_LEVELS;
    static const int MAX_ROUNDS = 4; // 每个档位读取4次

    // 私有方法
    void showUserPrompt();
    void startDataCollection();
    void processCurrentLevelData();
    void moveToNextLevel();
    void moveToNextChannel();
    void finishCalibration(bool success);
    double calculateTolerance(double measuredVoltage);

    // 通信相关
    QPair<bool, QString> sendCommand(const QByteArray &command, const QString &commandId);
    QByteArray createModbusCommand(const QString &hexString);
    QByteArray createModbusReadFrame(quint8 deviceAddr, quint16 startAddr, int channel, int count);
    double readVoltageValue(int channel);
    uint16_t calculateCRC16(const QByteArray &data);
    quint16 getReadAddress() const;
};

#endif // TC1618AVOLTAGEVERIFICATIONWORKER_H
